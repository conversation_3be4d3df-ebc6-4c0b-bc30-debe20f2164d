"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[2491],{62491:(e,t,i)=>{i.r(t);i.d(t,{RegExpCursor:()=>f,SearchCursor:()=>c,SearchQuery:()=>z,closeSearchPanel:()=>pe,findNext:()=>re,findPrevious:()=>ne,getSearchQuery:()=>J,gotoLine:()=>b,highlightSelectionMatches:()=>M,openSearchPanel:()=>de,replaceAll:()=>ae,replaceNext:()=>le,search:()=>I,searchKeymap:()=>me,searchPanelOpen:()=>X,selectMatches:()=>se,selectNextOccurrence:()=>R,selectSelectionMatches:()=>oe,setSearchQuery:()=>G});var r=i(22819);var n=i.n(r);var s=i(71674);var o=i.n(s);var l=i(41107);const a=typeof String.prototype.normalize=="function"?e=>e.normalize("NFKD"):e=>e;class c{constructor(e,t,i=0,r=e.length,n,s){this.test=s;this.value={from:0,to:0};this.done=false;this.matches=[];this.buffer="";this.bufferPos=0;this.iter=e.iterRange(i,r);this.bufferStart=i;this.normalize=n?e=>n(a(e)):a;this.query=this.normalize(t)}peek(){if(this.bufferPos==this.buffer.length){this.bufferStart+=this.buffer.length;this.iter.next();if(this.iter.done)return-1;this.bufferPos=0;this.buffer=this.iter.value}return(0,s.codePointAt)(this.buffer,this.bufferPos)}next(){while(this.matches.length)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let e=this.peek();if(e<0){this.done=true;return this}let t=(0,s.fromCodePoint)(e),i=this.bufferStart+this.bufferPos;this.bufferPos+=(0,s.codePointSize)(e);let r=this.normalize(t);if(r.length)for(let n=0,s=i;;n++){let e=r.charCodeAt(n);let o=this.match(e,s,this.bufferPos+this.bufferStart);if(n==r.length-1){if(o){this.value=o;return this}break}if(s==i&&n<t.length&&t.charCodeAt(n)==e)s++}}}match(e,t,i){let r=null;for(let n=0;n<this.matches.length;n+=2){let t=this.matches[n],s=false;if(this.query.charCodeAt(t)==e){if(t==this.query.length-1){r={from:this.matches[n+1],to:i}}else{this.matches[n]++;s=true}}if(!s){this.matches.splice(n,2);n-=2}}if(this.query.charCodeAt(0)==e){if(this.query.length==1)r={from:t,to:i};else this.matches.push(1,t)}if(r&&this.test&&!this.test(r.from,r.to,this.buffer,this.bufferStart))r=null;return r}}if(typeof Symbol!="undefined")c.prototype[Symbol.iterator]=function(){return this};const h={from:-1,to:-1,match:/.*/.exec("")};const u="gm"+(/x/.unicode==null?"":"u");class f{constructor(e,t,i,r=0,n=e.length){this.text=e;this.to=n;this.curLine="";this.done=false;this.value=h;if(/\\[sWDnr]|\n|\r|\[\^/.test(t))return new m(e,t,i,r,n);this.re=new RegExp(t,u+((i===null||i===void 0?void 0:i.ignoreCase)?"i":""));this.test=i===null||i===void 0?void 0:i.test;this.iter=e.iter();let s=e.lineAt(r);this.curLineStart=s.from;this.matchPos=v(e,r);this.getLine(this.curLineStart)}getLine(e){this.iter.next(e);if(this.iter.lineBreak){this.curLine=""}else{this.curLine=this.iter.value;if(this.curLineStart+this.curLine.length>this.to)this.curLine=this.curLine.slice(0,this.to-this.curLineStart);this.iter.next()}}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1;if(this.curLineStart>this.to)this.curLine="";else this.getLine(0)}next(){for(let e=this.matchPos-this.curLineStart;;){this.re.lastIndex=e;let t=this.matchPos<=this.to&&this.re.exec(this.curLine);if(t){let i=this.curLineStart+t.index,r=i+t[0].length;this.matchPos=v(this.text,r+(i==r?1:0));if(i==this.curLineStart+this.curLine.length)this.nextLine();if((i<r||i>this.value.to)&&(!this.test||this.test(i,r,t))){this.value={from:i,to:r,match:t};return this}e=this.matchPos-this.curLineStart}else if(this.curLineStart+this.curLine.length<this.to){this.nextLine();e=0}else{this.done=true;return this}}}}const d=new WeakMap;class p{constructor(e,t){this.from=e;this.text=t}get to(){return this.from+this.text.length}static get(e,t,i){let r=d.get(e);if(!r||r.from>=i||r.to<=t){let r=new p(t,e.sliceString(t,i));d.set(e,r);return r}if(r.from==t&&r.to==i)return r;let{text:n,from:s}=r;if(s>t){n=e.sliceString(t,s)+n;s=t}if(r.to<i)n+=e.sliceString(r.to,i);d.set(e,new p(s,n));return new p(t,n.slice(t-s,i-s))}}class m{constructor(e,t,i,r,n){this.text=e;this.to=n;this.done=false;this.value=h;this.matchPos=v(e,r);this.re=new RegExp(t,u+((i===null||i===void 0?void 0:i.ignoreCase)?"i":""));this.test=i===null||i===void 0?void 0:i.test;this.flat=p.get(e,r,this.chunkEnd(r+5e3))}chunkEnd(e){return e>=this.to?this.to:this.text.lineAt(e).to}next(){for(;;){let e=this.re.lastIndex=this.matchPos-this.flat.from;let t=this.re.exec(this.flat.text);if(t&&!t[0]&&t.index==e){this.re.lastIndex=e+1;t=this.re.exec(this.flat.text)}if(t){let e=this.flat.from+t.index,i=e+t[0].length;if((this.flat.to>=this.to||t.index+t[0].length<=this.flat.text.length-10)&&(!this.test||this.test(e,i,t))){this.value={from:e,to:i,match:t};this.matchPos=v(this.text,i+(e==i?1:0));return this}}if(this.flat.to==this.to){this.done=true;return this}this.flat=p.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+this.flat.text.length*2))}}}if(typeof Symbol!="undefined"){f.prototype[Symbol.iterator]=m.prototype[Symbol.iterator]=function(){return this}}function g(e){try{new RegExp(e,u);return true}catch(t){return false}}function v(e,t){if(t>=e.length)return t;let i=e.lineAt(t),r;while(t<i.to&&(r=i.text.charCodeAt(t-i.from))>=56320&&r<57344)t++;return t}function x(e){let t=String(e.state.doc.lineAt(e.state.selection.main.head).number);let i=(0,l.A)("input",{class:"cm-textfield",name:"line",value:t});let n=(0,l.A)("form",{class:"cm-gotoLine",onkeydown:t=>{if(t.keyCode==27){t.preventDefault();e.dispatch({effects:y.of(false)});e.focus()}else if(t.keyCode==13){t.preventDefault();o()}},onsubmit:e=>{e.preventDefault();o()}},(0,l.A)("label",e.state.phrase("Go to line"),": ",i)," ",(0,l.A)("button",{class:"cm-button",type:"submit"},e.state.phrase("go")),(0,l.A)("button",{name:"close",onclick:()=>{e.dispatch({effects:y.of(false)});e.focus()},"aria-label":e.state.phrase("close"),type:"button"},["×"]));function o(){let t=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(i.value);if(!t)return;let{state:n}=e,o=n.doc.lineAt(n.selection.main.head);let[,l,a,c,h]=t;let u=c?+c.slice(1):0;let f=a?+a:o.number;if(a&&h){let e=f/100;if(l)e=e*(l=="-"?-1:1)+o.number/n.doc.lines;f=Math.round(n.doc.lines*e)}else if(a&&l){f=f*(l=="-"?-1:1)+o.number}let d=n.doc.line(Math.max(1,Math.min(n.doc.lines,f)));let p=s.EditorSelection.cursor(d.from+Math.max(0,Math.min(u,d.length)));e.dispatch({effects:[y.of(false),r.EditorView.scrollIntoView(p.from,{y:"center"})],selection:p});e.focus()}return{dom:n}}const y=s.StateEffect.define();const w=s.StateField.define({create(){return true},update(e,t){for(let i of t.effects)if(i.is(y))e=i.value;return e},provide:e=>r.showPanel.from(e,(e=>e?x:null))});const b=e=>{let t=(0,r.getPanel)(e,x);if(!t){let i=[y.of(true)];if(e.state.field(w,false)==null)i.push(s.StateEffect.appendConfig.of([w,S]));e.dispatch({effects:i});t=(0,r.getPanel)(e,x)}if(t)t.dom.querySelector("input").select();return true};const S=r.EditorView.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px",position:"relative","& label":{fontSize:"80%"},"& [name=close]":{position:"absolute",top:"0",bottom:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:"0"}}});const C={highlightWordAroundCursor:false,minSelectionLength:1,maxMatches:100,wholeWords:false};const k=s.Facet.define({combine(e){return(0,s.combineConfig)(e,C,{highlightWordAroundCursor:(e,t)=>e||t,minSelectionLength:Math.min,maxMatches:Math.min})}});function M(e){let t=[P,L];if(e)t.push(k.of(e));return t}const A=r.Decoration.mark({class:"cm-selectionMatch"});const E=r.Decoration.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function q(e,t,i,r){return(i==0||e(t.sliceDoc(i-1,i))!=s.CharCategory.Word)&&(r==t.doc.length||e(t.sliceDoc(r,r+1))!=s.CharCategory.Word)}function D(e,t,i,r){return e(t.sliceDoc(i,i+1))==s.CharCategory.Word&&e(t.sliceDoc(r-1,r))==s.CharCategory.Word}const L=r.ViewPlugin.fromClass(class{constructor(e){this.decorations=this.getDeco(e)}update(e){if(e.selectionSet||e.docChanged||e.viewportChanged)this.decorations=this.getDeco(e.view)}getDeco(e){let t=e.state.facet(k);let{state:i}=e,n=i.selection;if(n.ranges.length>1)return r.Decoration.none;let s=n.main,o,l=null;if(s.empty){if(!t.highlightWordAroundCursor)return r.Decoration.none;let e=i.wordAt(s.head);if(!e)return r.Decoration.none;l=i.charCategorizer(s.head);o=i.sliceDoc(e.from,e.to)}else{let e=s.to-s.from;if(e<t.minSelectionLength||e>200)return r.Decoration.none;if(t.wholeWords){o=i.sliceDoc(s.from,s.to);l=i.charCategorizer(s.head);if(!(q(l,i,s.from,s.to)&&D(l,i,s.from,s.to)))return r.Decoration.none}else{o=i.sliceDoc(s.from,s.to);if(!o)return r.Decoration.none}}let a=[];for(let h of e.visibleRanges){let e=new c(i.doc,o,h.from,h.to);while(!e.next().done){let{from:n,to:o}=e.value;if(!l||q(l,i,n,o)){if(s.empty&&n<=s.from&&o>=s.to)a.push(E.range(n,o));else if(n>=s.to||o<=s.from)a.push(A.range(n,o));if(a.length>t.maxMatches)return r.Decoration.none}}}return r.Decoration.set(a)}},{decorations:e=>e.decorations});const P=r.EditorView.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}});const W=({state:e,dispatch:t})=>{let{selection:i}=e;let r=s.EditorSelection.create(i.ranges.map((t=>e.wordAt(t.head)||s.EditorSelection.cursor(t.head))),i.mainIndex);if(r.eq(i))return false;t(e.update({selection:r}));return true};function F(e,t){let{main:i,ranges:r}=e.selection;let n=e.wordAt(i.head),s=n&&n.from==i.from&&n.to==i.to;for(let o=false,l=new c(e.doc,t,r[r.length-1].to);;){l.next();if(l.done){if(o)return null;l=new c(e.doc,t,0,Math.max(0,r[r.length-1].from-1));o=true}else{if(o&&r.some((e=>e.from==l.value.from)))continue;if(s){let t=e.wordAt(l.value.from);if(!t||t.from!=l.value.from||t.to!=l.value.to)continue}return l.value}}}const R=({state:e,dispatch:t})=>{let{ranges:i}=e.selection;if(i.some((e=>e.from===e.to)))return W({state:e,dispatch:t});let n=e.sliceDoc(i[0].from,i[0].to);if(e.selection.ranges.some((t=>e.sliceDoc(t.from,t.to)!=n)))return false;let o=F(e,n);if(!o)return false;t(e.update({selection:e.selection.addRange(s.EditorSelection.range(o.from,o.to),false),effects:r.EditorView.scrollIntoView(o.to)}));return true};const V=s.Facet.define({combine(e){return(0,s.combineConfig)(e,{top:false,caseSensitive:false,literal:false,regexp:false,wholeWord:false,createPanel:e=>new ge(e),scrollToMatch:e=>r.EditorView.scrollIntoView(e)})}});function I(e){return e?[V.of(e),Se]:Se}class z{constructor(e){this.search=e.search;this.caseSensitive=!!e.caseSensitive;this.literal=!!e.literal;this.regexp=!!e.regexp;this.replace=e.replace||"";this.valid=!!this.search&&(!this.regexp||g(this.search));this.unquoted=this.unquote(this.search);this.wholeWord=!!e.wholeWord}unquote(e){return this.literal?e:e.replace(/\\([nrt\\])/g,((e,t)=>t=="n"?"\n":t=="r"?"\r":t=="t"?"\t":"\\"))}eq(e){return this.search==e.search&&this.replace==e.replace&&this.caseSensitive==e.caseSensitive&&this.regexp==e.regexp&&this.wholeWord==e.wholeWord}create(){return this.regexp?new K(this):new _(this)}getCursor(e,t=0,i){let r=e.doc?e:s.EditorState.create({doc:e});if(i==null)i=r.doc.length;return this.regexp?N(this,r,t,i):T(this,r,t,i)}}class O{constructor(e){this.spec=e}}function T(e,t,i,r){return new c(t.doc,e.unquoted,i,r,e.caseSensitive?undefined:e=>e.toLowerCase(),e.wholeWord?$(t.doc,t.charCategorizer(t.selection.main.head)):undefined)}function $(e,t){return(i,r,n,o)=>{if(o>i||o+n.length<r){o=Math.max(0,i-2);n=e.sliceString(o,Math.min(e.length,r+2))}return(t(Q(n,i-o))!=s.CharCategory.Word||t(j(n,i-o))!=s.CharCategory.Word)&&(t(j(n,r-o))!=s.CharCategory.Word||t(Q(n,r-o))!=s.CharCategory.Word)}}class _ extends O{constructor(e){super(e)}nextMatch(e,t,i){let r=T(this.spec,e,i,e.doc.length).nextOverlapping();if(r.done){let i=Math.min(e.doc.length,t+this.spec.unquoted.length);r=T(this.spec,e,0,i).nextOverlapping()}return r.done||r.value.from==t&&r.value.to==i?null:r.value}prevMatchInRange(e,t,i){for(let r=i;;){let i=Math.max(t,r-1e4-this.spec.unquoted.length);let n=T(this.spec,e,i,r),s=null;while(!n.nextOverlapping().done)s=n.value;if(s)return s;if(i==t)return null;r-=1e4}}prevMatch(e,t,i){let r=this.prevMatchInRange(e,0,t);if(!r)r=this.prevMatchInRange(e,Math.max(0,i-this.spec.unquoted.length),e.doc.length);return r&&(r.from!=t||r.to!=i)?r:null}getReplacement(e){return this.spec.unquote(this.spec.replace)}matchAll(e,t){let i=T(this.spec,e,0,e.doc.length),r=[];while(!i.next().done){if(r.length>=t)return null;r.push(i.value)}return r}highlight(e,t,i,r){let n=T(this.spec,e,Math.max(0,t-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,e.doc.length));while(!n.next().done)r(n.value.from,n.value.to)}}function N(e,t,i,r){return new f(t.doc,e.search,{ignoreCase:!e.caseSensitive,test:e.wholeWord?B(t.charCategorizer(t.selection.main.head)):undefined},i,r)}function Q(e,t){return e.slice((0,s.findClusterBreak)(e,t,false),t)}function j(e,t){return e.slice(t,(0,s.findClusterBreak)(e,t))}function B(e){return(t,i,r)=>!r[0].length||(e(Q(r.input,r.index))!=s.CharCategory.Word||e(j(r.input,r.index))!=s.CharCategory.Word)&&(e(j(r.input,r.index+r[0].length))!=s.CharCategory.Word||e(Q(r.input,r.index+r[0].length))!=s.CharCategory.Word)}class K extends O{nextMatch(e,t,i){let r=N(this.spec,e,i,e.doc.length).next();if(r.done)r=N(this.spec,e,0,t).next();return r.done?null:r.value}prevMatchInRange(e,t,i){for(let r=1;;r++){let n=Math.max(t,i-r*1e4);let s=N(this.spec,e,n,i),o=null;while(!s.next().done)o=s.value;if(o&&(n==t||o.from>n+10))return o;if(n==t)return null}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.doc.length)}getReplacement(e){return this.spec.unquote(this.spec.replace).replace(/\$([$&]|\d+)/g,((t,i)=>{if(i=="&")return e.match[0];if(i=="$")return"$";for(let r=i.length;r>0;r--){let t=+i.slice(0,r);if(t>0&&t<e.match.length)return e.match[t]+i.slice(r)}return t}))}matchAll(e,t){let i=N(this.spec,e,0,e.doc.length),r=[];while(!i.next().done){if(r.length>=t)return null;r.push(i.value)}return r}highlight(e,t,i,r){let n=N(this.spec,e,Math.max(0,t-250),Math.min(i+250,e.doc.length));while(!n.next().done)r(n.value.from,n.value.to)}}const G=s.StateEffect.define();const H=s.StateEffect.define();const U=s.StateField.define({create(e){return new Y(he(e).create(),null)},update(e,t){for(let i of t.effects){if(i.is(G))e=new Y(i.value.create(),e.panel);else if(i.is(H))e=new Y(e.query,i.value?ce:null)}return e},provide:e=>r.showPanel.from(e,(e=>e.panel))});function J(e){let t=e.field(U,false);return t?t.query.spec:he(e)}function X(e){var t;return((t=e.field(U,false))===null||t===void 0?void 0:t.panel)!=null}class Y{constructor(e,t){this.query=e;this.panel=t}}const Z=r.Decoration.mark({class:"cm-searchMatch"}),ee=r.Decoration.mark({class:"cm-searchMatch cm-searchMatch-selected"});const te=r.ViewPlugin.fromClass(class{constructor(e){this.view=e;this.decorations=this.highlight(e.state.field(U))}update(e){let t=e.state.field(U);if(t!=e.startState.field(U)||e.docChanged||e.selectionSet||e.viewportChanged)this.decorations=this.highlight(t)}highlight({query:e,panel:t}){if(!t||!e.spec.valid)return r.Decoration.none;let{view:i}=this;let n=new s.RangeSetBuilder;for(let r=0,s=i.visibleRanges,o=s.length;r<o;r++){let{from:t,to:l}=s[r];while(r<o-1&&l>s[r+1].from-2*250)l=s[++r].to;e.highlight(i.state,t,l,((e,t)=>{let r=i.state.selection.ranges.some((i=>i.from==e&&i.to==t));n.add(e,t,r?ee:Z)}))}return n.finish()}},{decorations:e=>e.decorations});function ie(e){return t=>{let i=t.state.field(U,false);return i&&i.query.spec.valid?e(t,i):de(t)}}const re=ie(((e,{query:t})=>{let{to:i}=e.state.selection.main;let r=t.nextMatch(e.state,i,i);if(!r)return false;let n=s.EditorSelection.single(r.from,r.to);let o=e.state.facet(V);e.dispatch({selection:n,effects:[we(e,r),o.scrollToMatch(n.main,e)],userEvent:"select.search"});fe(e);return true}));const ne=ie(((e,{query:t})=>{let{state:i}=e,{from:r}=i.selection.main;let n=t.prevMatch(i,r,r);if(!n)return false;let o=s.EditorSelection.single(n.from,n.to);let l=e.state.facet(V);e.dispatch({selection:o,effects:[we(e,n),l.scrollToMatch(o.main,e)],userEvent:"select.search"});fe(e);return true}));const se=ie(((e,{query:t})=>{let i=t.matchAll(e.state,1e3);if(!i||!i.length)return false;e.dispatch({selection:s.EditorSelection.create(i.map((e=>s.EditorSelection.range(e.from,e.to)))),userEvent:"select.search.matches"});return true}));const oe=({state:e,dispatch:t})=>{let i=e.selection;if(i.ranges.length>1||i.main.empty)return false;let{from:r,to:n}=i.main;let o=[],l=0;for(let a=new c(e.doc,e.sliceDoc(r,n));!a.next().done;){if(o.length>1e3)return false;if(a.value.from==r)l=o.length;o.push(s.EditorSelection.range(a.value.from,a.value.to))}t(e.update({selection:s.EditorSelection.create(o,l),userEvent:"select.search.matches"}));return true};const le=ie(((e,{query:t})=>{let{state:i}=e,{from:n,to:o}=i.selection.main;if(i.readOnly)return false;let l=t.nextMatch(i,n,n);if(!l)return false;let a=l;let c=[],h,u;let f=[];if(a.from==n&&a.to==o){u=i.toText(t.getReplacement(a));c.push({from:a.from,to:a.to,insert:u});a=t.nextMatch(i,a.from,a.to);f.push(r.EditorView.announce.of(i.phrase("replaced match on line $",i.doc.lineAt(n).number)+"."))}if(a){let t=c.length==0||c[0].from>=l.to?0:l.to-l.from-u.length;h=s.EditorSelection.single(a.from-t,a.to-t);f.push(we(e,a));f.push(i.facet(V).scrollToMatch(h.main,e))}e.dispatch({changes:c,selection:h,effects:f,userEvent:"input.replace"});return true}));const ae=ie(((e,{query:t})=>{if(e.state.readOnly)return false;let i=t.matchAll(e.state,1e9).map((e=>{let{from:i,to:r}=e;return{from:i,to:r,insert:t.getReplacement(e)}}));if(!i.length)return false;let n=e.state.phrase("replaced $ matches",i.length)+".";e.dispatch({changes:i,effects:r.EditorView.announce.of(n),userEvent:"input.replace.all"});return true}));function ce(e){return e.state.facet(V).createPanel(e)}function he(e,t){var i,r,n,s,o;let l=e.selection.main;let a=l.empty||l.to>l.from+100?"":e.sliceDoc(l.from,l.to);if(t&&!a)return t;let c=e.facet(V);return new z({search:((i=t===null||t===void 0?void 0:t.literal)!==null&&i!==void 0?i:c.literal)?a:a.replace(/\n/g,"\\n"),caseSensitive:(r=t===null||t===void 0?void 0:t.caseSensitive)!==null&&r!==void 0?r:c.caseSensitive,literal:(n=t===null||t===void 0?void 0:t.literal)!==null&&n!==void 0?n:c.literal,regexp:(s=t===null||t===void 0?void 0:t.regexp)!==null&&s!==void 0?s:c.regexp,wholeWord:(o=t===null||t===void 0?void 0:t.wholeWord)!==null&&o!==void 0?o:c.wholeWord})}function ue(e){let t=(0,r.getPanel)(e,ce);return t&&t.dom.querySelector("[main-field]")}function fe(e){let t=ue(e);if(t&&t==e.root.activeElement)t.select()}const de=e=>{let t=e.state.field(U,false);if(t&&t.panel){let i=ue(e);if(i&&i!=e.root.activeElement){let r=he(e.state,t.query.spec);if(r.valid)e.dispatch({effects:G.of(r)});i.focus();i.select()}}else{e.dispatch({effects:[H.of(true),t?G.of(he(e.state,t.query.spec)):s.StateEffect.appendConfig.of(Se)]})}return true};const pe=e=>{let t=e.state.field(U,false);if(!t||!t.panel)return false;let i=(0,r.getPanel)(e,ce);if(i&&i.dom.contains(e.root.activeElement))e.focus();e.dispatch({effects:H.of(false)});return true};const me=[{key:"Mod-f",run:de,scope:"editor search-panel"},{key:"F3",run:re,shift:ne,scope:"editor search-panel",preventDefault:true},{key:"Mod-g",run:re,shift:ne,scope:"editor search-panel",preventDefault:true},{key:"Escape",run:pe,scope:"editor search-panel"},{key:"Mod-Shift-l",run:oe},{key:"Mod-Alt-g",run:b},{key:"Mod-d",run:R,preventDefault:true}];class ge{constructor(e){this.view=e;let t=this.query=e.state.field(U).query.spec;this.commit=this.commit.bind(this);this.searchField=(0,l.A)("input",{value:t.search,placeholder:ve(e,"Find"),"aria-label":ve(e,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit});this.replaceField=(0,l.A)("input",{value:t.replace,placeholder:ve(e,"Replace"),"aria-label":ve(e,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit});this.caseField=(0,l.A)("input",{type:"checkbox",name:"case",form:"",checked:t.caseSensitive,onchange:this.commit});this.reField=(0,l.A)("input",{type:"checkbox",name:"re",form:"",checked:t.regexp,onchange:this.commit});this.wordField=(0,l.A)("input",{type:"checkbox",name:"word",form:"",checked:t.wholeWord,onchange:this.commit});function i(e,t,i){return(0,l.A)("button",{class:"cm-button",name:e,onclick:t,type:"button"},i)}this.dom=(0,l.A)("div",{onkeydown:e=>this.keydown(e),class:"cm-search"},[this.searchField,i("next",(()=>re(e)),[ve(e,"next")]),i("prev",(()=>ne(e)),[ve(e,"previous")]),i("select",(()=>se(e)),[ve(e,"all")]),(0,l.A)("label",null,[this.caseField,ve(e,"match case")]),(0,l.A)("label",null,[this.reField,ve(e,"regexp")]),(0,l.A)("label",null,[this.wordField,ve(e,"by word")]),...e.state.readOnly?[]:[(0,l.A)("br"),this.replaceField,i("replace",(()=>le(e)),[ve(e,"replace")]),i("replaceAll",(()=>ae(e)),[ve(e,"replace all")])],(0,l.A)("button",{name:"close",onclick:()=>pe(e),"aria-label":ve(e,"close"),type:"button"},["×"])])}commit(){let e=new z({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});if(!e.eq(this.query)){this.query=e;this.view.dispatch({effects:G.of(e)})}}keydown(e){if((0,r.runScopeHandlers)(this.view,e,"search-panel")){e.preventDefault()}else if(e.keyCode==13&&e.target==this.searchField){e.preventDefault();(e.shiftKey?ne:re)(this.view)}else if(e.keyCode==13&&e.target==this.replaceField){e.preventDefault();le(this.view)}}update(e){for(let t of e.transactions)for(let e of t.effects){if(e.is(G)&&!e.value.eq(this.query))this.setQuery(e.value)}}setQuery(e){this.query=e;this.searchField.value=e.search;this.replaceField.value=e.replace;this.caseField.checked=e.caseSensitive;this.reField.checked=e.regexp;this.wordField.checked=e.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(V).top}}function ve(e,t){return e.state.phrase(t)}const xe=30;const ye=/[\s\.,:;?!]/;function we(e,{from:t,to:i}){let n=e.state.doc.lineAt(t),s=e.state.doc.lineAt(i).to;let o=Math.max(n.from,t-xe),l=Math.min(s,i+xe);let a=e.state.sliceDoc(o,l);if(o!=n.from){for(let e=0;e<xe;e++)if(!ye.test(a[e+1])&&ye.test(a[e])){a=a.slice(e);break}}if(l!=s){for(let e=a.length-1;e>a.length-xe;e--)if(!ye.test(a[e-1])&&ye.test(a[e])){a=a.slice(0,e);break}}return r.EditorView.announce.of(`${e.state.phrase("current match")}. ${a} ${e.state.phrase("on line")} ${n.number}.`)}const be=r.EditorView.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}});const Se=[U,s.Prec.low(te),be]},41107:(e,t,i)=>{i.d(t,{A:()=>r});function r(){var e=arguments[0];if(typeof e=="string")e=document.createElement(e);var t=1,i=arguments[1];if(i&&typeof i=="object"&&i.nodeType==null&&!Array.isArray(i)){for(var r in i)if(Object.prototype.hasOwnProperty.call(i,r)){var s=i[r];if(typeof s=="string")e.setAttribute(r,s);else if(s!=null)e[r]=s}t++}for(;t<arguments.length;t++)n(e,arguments[t]);return e}function n(e,t){if(typeof t=="string"){e.appendChild(document.createTextNode(t))}else if(t==null){}else if(t.nodeType!=null){e.appendChild(t)}else if(Array.isArray(t)){for(var i=0;i<t.length;i++)n(e,t[i])}else{throw new RangeError("Unsupported child node: "+t)}}}}]);