# 📈 中证1000指数每日上涨概率统计

这是一个简洁易用的股市分析工具，专门统计中证1000指数在一个星期每一天（工作日）的上涨概率。使用Streamlit构建，界面友好，特别适合老人家使用。

## ✨ 主要特点

- 🌐 **网页界面**：使用Streamlit构建，操作简单直观
- 📊 **实时数据**：使用akshare获取最新的中证1000指数数据
- 📈 **清晰图表**：大字体、高对比度，老人家也能轻松查看
- 📋 **详细统计**：每个工作日的上涨概率、平均收益率等指标
- 💾 **数据下载**：支持CSV格式数据下载
- 🔧 **网络优化**：解决了akshare连接问题，数据获取稳定

## 安装依赖

本项目使用 `uv` 作为包管理器。请确保已安装 `uv`：

```bash
# 安装 uv (如果尚未安装)
pip install uv

# 安装项目依赖
uv sync
```

## 🚀 使用方法

### 第一步：安装依赖

```bash
# 确保已安装uv包管理器
pip install uv

# 安装项目依赖
uv sync
```

### 第二步：启动应用

```bash
# 启动Streamlit网页应用
uv run streamlit run 股市分析.py
```

### 第三步：打开浏览器

程序启动后会显示本地地址，通常是：
- 本地访问：http://localhost:8501
- 网络访问：http://你的IP地址:8501

直接在浏览器中打开即可使用！

## 📊 功能介绍

### 主界面功能

1. **📈 实时数据展示**
   - 分析时间段、总交易日数
   - 总上涨天数、整体上涨概率

2. **📊 可视化图表**
   - 各工作日上涨概率柱状图（大字体，易读）
   - 各工作日平均收益率对比图
   - 数据表格展示详细统计

3. **🔍 智能分析**
   - 自动识别上涨概率最高/最低的工作日
   - 平均收益率最高/最低的工作日
   - 概率差异分析和投资建议

4. **💾 数据下载**
   - 统计数据CSV下载
   - 原始数据CSV下载

## 分析指标

- **上涨概率**: 每个工作日上涨的概率（收益率 > 0）
- **平均收益率**: 每个工作日的平均日收益率
- **收益率标准差**: 收益率的波动性指标
- **交易日数**: 统计期间内每个工作日的交易天数

## 数据源

- 使用 [akshare](https://akshare.akfamily.xyz/) 获取中证1000指数数据
- 指数代码: 000852
- 数据时间范围: 最近一年

## 依赖包

- `akshare`: 金融数据接口
- `pandas`: 数据处理
- `numpy`: 数值计算
- `matplotlib`: 图表绘制
- `seaborn`: 统计图表

## 程序说明

### 📁 项目结构

```
股市统计/
├── 股市分析.py              # 🌟 主程序文件（Streamlit应用）
├── pyproject.toml          # 项目配置和依赖
├── README.md               # 项目说明文档
├── uv.lock                 # 依赖锁定文件
└── backup/                 # 备份文件夹
    ├── final_analysis.py   # 命令行版本
    ├── akshare_fix.py      # 网络修复版本
    └── 其他历史版本...
```

## 🎯 特别优化（适合老人家使用）

- **🔤 大字体显示**: 图表和文字都使用大字体，清晰易读
- **🎨 高对比度**: 使用鲜明的颜色对比，方便识别
- **📱 简洁界面**: 去除复杂功能，只保留核心分析内容
- **🖱️ 一键操作**: 打开网页即可使用，无需复杂操作
- **💾 便捷下载**: 一键下载分析结果，方便保存
- **🔧 稳定可靠**: 解决了网络连接问题，数据获取稳定

## 🔧 技术特点

- **网络问题解决**: 使用最稳定的东方财富数据源，解决akshare连接问题
- **中文字体优化**: 使用Plotly图表库，完美支持中文显示
- **响应式设计**: 自适应不同屏幕尺寸，手机电脑都能用
- **数据缓存**: 智能缓存机制，避免重复请求数据
- **错误处理**: 完善的错误处理机制，网络异常时友好提示

## ⚠️ 使用说明

- **网络要求**: 需要网络连接获取最新数据，首次运行可能需要等待
- **分析范围**: 只分析工作日数据（周一至周五），节假日自动排除
- **数据来源**: 使用akshare库从东方财富获取中证1000指数数据
- **浏览器兼容**: 支持Chrome、Firefox、Safari等主流浏览器
- **系统要求**: Windows、macOS、Linux均可运行

## 🎯 实际分析结果

基于2023年至2024年真实数据的分析结果显示：

- **星期一**上涨概率最高：**59.38%**
- **星期三**上涨概率最低：**40.21%**
- **星期四**平均收益率最高：**0.35%**
- **星期三**平均收益率最低：**-0.17%**
- 不同工作日的上涨概率差异：**19.17%**

### 📈 关键发现

1. **周一效应明显**：星期一的上涨概率显著高于其他工作日
2. **周三表现最弱**：无论是上涨概率还是平均收益率都是最低的
3. **周四收益最佳**：虽然上涨概率中等，但平均收益率最高
4. **整体上涨概率**：49.07%，略低于50%
5. **日均收益率**：0.0568%，年化约14%

## 许可证

MIT License
