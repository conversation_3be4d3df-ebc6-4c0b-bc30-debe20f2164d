# 中证1000指数每日上涨概率统计

这个项目用于统计中证1000指数在一个星期的每一天（工作日）上涨的概率，使用akshare获取最近一年的数据。

## 功能特点

- 📊 获取中证1000指数最近一年的历史数据
- 📈 计算每个工作日的上涨概率
- 📉 分析平均收益率和波动性
- 🎨 生成可视化图表
- 📋 导出详细统计报告

## 安装依赖

本项目使用 `uv` 作为包管理器。请确保已安装 `uv`：

```bash
# 安装 uv (如果尚未安装)
pip install uv

# 安装项目依赖
uv sync
```

## 使用方法

### 方法一：运行完整版程序（推荐）

```bash
# 运行主程序（包含完整的可视化和分析功能）
uv run python main.py
```

### 方法二：运行简化版程序

```bash
# 运行简化版程序（专门用于获取真实数据）
uv run python simple_analysis.py
```

### 方法三：运行测试程序

```bash
# 测试程序功能和输出文件
uv run python test_analysis.py
```

## 输出结果

程序运行后会生成以下文件：

1. **中证1000指数每日上涨概率分析.png** - 包含4个子图的可视化分析：
   - 各工作日上涨概率柱状图
   - 各工作日平均收益率柱状图
   - 各工作日交易日数统计
   - 各工作日收益率分布箱线图

2. **中证1000指数每日上涨概率统计.csv** - 详细的统计数据表格

3. **中证1000指数历史数据.csv** - 原始历史数据

## 分析指标

- **上涨概率**: 每个工作日上涨的概率（收益率 > 0）
- **平均收益率**: 每个工作日的平均日收益率
- **收益率标准差**: 收益率的波动性指标
- **交易日数**: 统计期间内每个工作日的交易天数

## 数据源

- 使用 [akshare](https://akshare.akfamily.xyz/) 获取中证1000指数数据
- 指数代码: 000852
- 数据时间范围: 最近一年

## 依赖包

- `akshare`: 金融数据接口
- `pandas`: 数据处理
- `numpy`: 数值计算
- `matplotlib`: 图表绘制
- `seaborn`: 统计图表

## 程序说明

### 主要文件

- **main.py**: 完整版程序，包含数据获取、分析、可视化等全部功能
- **simple_analysis.py**: 简化版程序，专门用于获取真实数据
- **test_analysis.py**: 测试程序，用于验证输出文件和数据质量

### 功能特点

- **智能数据获取**: 优先尝试获取真实数据，网络失败时自动使用模拟数据演示
- **多重重试机制**: 网络请求失败时自动重试，提高数据获取成功率
- **完整的统计分析**: 计算每个工作日的上涨概率、平均收益率、波动性等指标
- **可视化图表**: 生成包含4个子图的综合分析图表
- **数据导出**: 自动保存CSV格式的统计结果和原始数据

### 输出文件说明

1. **中证1000指数每日上涨概率分析.png**: 可视化分析图表
2. **中证1000指数每日上涨概率统计.csv**: 按工作日统计的概率数据
3. **中证1000指数历史数据.csv**: 完整的历史价格数据

## 注意事项

- 程序只分析工作日数据（周一至周五）
- 需要网络连接以获取最新数据
- 首次运行可能需要较长时间下载数据
- 如果网络获取失败，程序会自动使用模拟数据进行演示
- 图表使用非交互式后端，适合在服务器环境运行

## 示例结果

基于模拟数据的分析结果显示：
- **星期一**上涨概率最高（63.46%）
- **星期三**上涨概率最低（42.31%）
- 不同工作日的上涨概率存在明显差异（21.15%的差异范围）

## 许可证

MIT License
