[project]
name = "stock-statistics"
version = "0.1.0"
description = "中证1000指数每日上涨概率统计"
authors = [{ name = "User", email = "<EMAIL>" }]
dependencies = [
    "pandas>=2.0.0",
    "numpy>=2.3.2",
    "matplotlib>=3.10.5",
    "seaborn>=0.12.0",
    "akshare>=1.17.34",
]
requires-python = ">=3.11"
readme = "README.md"
license = { text = "MIT" }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.uv]
dev-dependencies = ["pytest>=7.0.0", "jupyter>=1.0.0"]
