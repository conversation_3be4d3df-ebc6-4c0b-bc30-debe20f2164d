#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中证1000指数每日上涨概率统计程序
"""

import pandas as pd
import os

def test_output_files():
    """
    测试输出文件是否正确生成
    """
    print("测试输出文件...")
    
    # 检查文件是否存在
    files_to_check = [
        "中证1000指数每日上涨概率统计.csv",
        "中证1000指数历史数据.csv", 
        "中证1000指数每日上涨概率分析.png"
    ]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            print(f"✓ {file_name} 文件存在")
        else:
            print(f"✗ {file_name} 文件不存在")
    
    # 检查CSV文件内容
    try:
        stats_df = pd.read_csv("中证1000指数每日上涨概率统计.csv", index_col=0)
        print(f"\n统计文件内容:")
        print(stats_df)
        
        # 验证数据合理性
        print(f"\n数据验证:")
        print(f"- 包含工作日数量: {len(stats_df)}")
        print(f"- 上涨概率范围: {stats_df['上涨概率'].min():.2%} - {stats_df['上涨概率'].max():.2%}")
        print(f"- 平均收益率范围: {stats_df['平均收益率'].min():.4%} - {stats_df['平均收益率'].max():.4%}")
        
        # 检查是否包含所有工作日
        expected_weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五']
        missing_weekdays = set(expected_weekdays) - set(stats_df.index)
        if missing_weekdays:
            print(f"✗ 缺少工作日: {missing_weekdays}")
        else:
            print("✓ 包含所有工作日数据")
            
    except Exception as e:
        print(f"✗ 读取统计文件时出错: {e}")
    
    try:
        data_df = pd.read_csv("中证1000指数历史数据.csv")
        print(f"\n历史数据文件:")
        print(f"- 数据条数: {len(data_df)}")
        print(f"- 列数: {len(data_df.columns)}")
        print(f"- 列名: {list(data_df.columns)}")
        print(f"- 时间范围: {data_df['日期'].min()} 到 {data_df['日期'].max()}")
        
    except Exception as e:
        print(f"✗ 读取历史数据文件时出错: {e}")

def test_data_quality():
    """
    测试数据质量
    """
    print("\n" + "="*50)
    print("数据质量测试")
    print("="*50)
    
    try:
        data_df = pd.read_csv("中证1000指数历史数据.csv")
        data_df['日期'] = pd.to_datetime(data_df['日期'])
        
        # 检查数据完整性
        print(f"数据完整性检查:")
        print(f"- 总记录数: {len(data_df)}")
        print(f"- 缺失值数量: {data_df.isnull().sum().sum()}")
        
        # 检查收益率分布
        if '收益率' in data_df.columns:
            returns = data_df['收益率'].dropna()
            print(f"\n收益率统计:")
            print(f"- 平均收益率: {returns.mean():.4%}")
            print(f"- 收益率标准差: {returns.std():.4%}")
            print(f"- 最大收益率: {returns.max():.4%}")
            print(f"- 最小收益率: {returns.min():.4%}")
            print(f"- 上涨天数: {(returns > 0).sum()}")
            print(f"- 下跌天数: {(returns < 0).sum()}")
            print(f"- 平盘天数: {(returns == 0).sum()}")
        
        # 检查工作日分布
        if '星期几_中文' in data_df.columns:
            weekday_counts = data_df['星期几_中文'].value_counts()
            print(f"\n工作日分布:")
            for day, count in weekday_counts.items():
                print(f"- {day}: {count}天")
                
    except Exception as e:
        print(f"✗ 数据质量测试失败: {e}")

if __name__ == "__main__":
    print("中证1000指数每日上涨概率统计程序测试")
    print("="*60)
    
    test_output_files()
    test_data_quality()
    
    print("\n测试完成！")
