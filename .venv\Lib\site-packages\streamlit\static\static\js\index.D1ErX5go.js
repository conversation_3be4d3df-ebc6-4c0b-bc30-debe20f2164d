import{e as We,by as J,r as x,c2 as ht,bR as gt,bz as G,c3 as pt,c4 as vt,s as Ke,ax as mt,bJ as bt,ap as yt,aS as Tt,cv as Ot,j as ae,aE as kt,bu as wt,bL as Rt,bv as St,bc as Mt,bw as _t,J as $t,cw as _e}from"./index.DKN5MVff.js";import{s as Fe}from"./sprintf.D7DtBTRn.js";import{a as Et}from"./useBasicWidgetState.DB3vMS9V.js";import"./FormClearHelper.DF4gFAOO.js";var se={},U={},ue={},ce={},je;function $e(){if(je)return ce;je=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.Direction=void 0;var e;return function(r){r.Right="to right",r.Left="to left",r.Down="to bottom",r.Up="to top"}(e||(ce.Direction=e={})),ce}var Ve;function Xe(){return Ve||(Ve=1,function(e){var r=ue&&ue.__spreadArray||function(i,s,c){if(c||arguments.length===2)for(var m=0,R=s.length,p;m<R;m++)(p||!(m in s))&&(p||(p=Array.prototype.slice.call(s,0,m)),p[m]=s[m]);return i.concat(p||Array.prototype.slice.call(s))};Object.defineProperty(e,"__esModule",{value:!0}),e.isIOS=e.useThumbOverlap=e.assertUnreachable=e.voidFn=e.getTrackBackground=e.replaceAt=e.schd=e.translate=e.getClosestThumbIndex=e.translateThumbs=e.getPaddingAndBorder=e.getMargin=e.checkInitialOverlap=e.checkValuesAgainstBoundaries=e.checkBoundaries=e.isVertical=e.relativeValue=e.normalizeValue=e.isStepDivisible=e.isTouchEvent=e.getStepDecimals=void 0;var n=We(),o=$e(),g=function(i){var s=i.toString().split(".")[1];return s?s.length:0};e.getStepDecimals=g;function _(i){return i.touches&&i.touches.length||i.changedTouches&&i.changedTouches.length}e.isTouchEvent=_;function u(i,s,c){var m=(s-i)/c,R=8,p=Number(m.toFixed(R));return parseInt(p.toString(),10)===p}e.isStepDivisible=u;function h(i,s,c,m,R,p,O){var $=1e11;if(i=Math.round(i*$)/$,!p){var D=O[s-1],I=O[s+1];if(D&&D>i)return D;if(I&&I<i)return I}if(i>m)return m;if(i<c)return c;var N=Math.floor(i*$-c*$)%Math.floor(R*$),V=Math.floor(i*$-Math.abs(N)),q=N===0?i:V/$,P=Math.abs(N/$)<R/2?q:q+R,B=(0,e.getStepDecimals)(R);return parseFloat(P.toFixed(B))}e.normalizeValue=h;function j(i,s,c){return(i-s)/(c-s)}e.relativeValue=j;function F(i){return i===o.Direction.Up||i===o.Direction.Down}e.isVertical=F;function A(i,s,c){if(s>=c)throw new RangeError("min (".concat(s,") is equal/bigger than max (").concat(c,")"));if(i<s)throw new RangeError("value (".concat(i,") is smaller than min (").concat(s,")"));if(i>c)throw new RangeError("value (".concat(i,") is bigger than max (").concat(c,")"))}e.checkBoundaries=A;function S(i,s,c){return i<s?s:i>c?c:i}e.checkValuesAgainstBoundaries=S;function y(i){if(!(i.length<2)&&!i.slice(1).every(function(s,c){return i[c]<=s}))throw new RangeError("values={[".concat(i,"]} needs to be sorted when allowOverlap={false}"))}e.checkInitialOverlap=y;function d(i){var s=window.getComputedStyle(i);return{top:parseInt(s["margin-top"],10),bottom:parseInt(s["margin-bottom"],10),left:parseInt(s["margin-left"],10),right:parseInt(s["margin-right"],10)}}e.getMargin=d;function t(i){var s=window.getComputedStyle(i);return{top:parseInt(s["padding-top"],10)+parseInt(s["border-top-width"],10),bottom:parseInt(s["padding-bottom"],10)+parseInt(s["border-bottom-width"],10),left:parseInt(s["padding-left"],10)+parseInt(s["border-left-width"],10),right:parseInt(s["padding-right"],10)+parseInt(s["border-right-width"],10)}}e.getPaddingAndBorder=t;function a(i,s,c){var m=c?-1:1;i.forEach(function(R,p){return v(R,m*s[p].x,s[p].y)})}e.translateThumbs=a;function l(i,s,c,m){for(var R=0,p=L(i[0],s,c,m),O=1;O<i.length;O++){var $=L(i[O],s,c,m);$<p&&(p=$,R=O)}return R}e.getClosestThumbIndex=l;function v(i,s,c){i.style.transform="translate(".concat(s,"px, ").concat(c,"px)")}e.translate=v;var f=function(i){var s=[],c=null,m=function(){for(var R=[],p=0;p<arguments.length;p++)R[p]=arguments[p];s=R,!c&&(c=requestAnimationFrame(function(){c=null,i.apply(void 0,s)}))};return m};e.schd=f;function b(i,s,c){var m=i.slice(0);return m[s]=c,m}e.replaceAt=b;function E(i){var s=i.values,c=i.colors,m=i.min,R=i.max,p=i.direction,O=p===void 0?o.Direction.Right:p,$=i.rtl,D=$===void 0?!1:$;D&&O===o.Direction.Right?O=o.Direction.Left:D&&o.Direction.Left&&(O=o.Direction.Right);var I=s.slice(0).sort(function(V,q){return V-q}).map(function(V){return(V-m)/(R-m)*100}),N=I.reduce(function(V,q,P){return"".concat(V,", ").concat(c[P]," ").concat(q,"%, ").concat(c[P+1]," ").concat(q,"%")},"");return"linear-gradient(".concat(O,", ").concat(c[0]," 0%").concat(N,", ").concat(c[c.length-1]," 100%)")}e.getTrackBackground=E;function k(){}e.voidFn=k;function T(i){throw new Error("Didn't expect to get here")}e.assertUnreachable=T;var w=function(i,s,c,m,R){R===void 0&&(R=function(O){return O});var p=Math.ceil(r([i],Array.from(i.children),!0).reduce(function(O,$){var D=Math.ceil($.getBoundingClientRect().width);if($.innerText&&$.innerText.includes(c)&&$.childElementCount===0){var I=$.cloneNode(!0);I.innerHTML=R(s.toFixed(m)),I.style.visibility="hidden",document.body.appendChild(I),D=Math.ceil(I.getBoundingClientRect().width),document.body.removeChild(I)}return D>O?D:O},i.getBoundingClientRect().width));return p},C=function(i,s,c,m,R,p,O){O===void 0&&(O=function(I){return I});var $=[],D=function(I){var N=w(c[I],m[I],R,p,O),V=s[I].x;s.forEach(function(q,P){var B=q.x,Z=w(c[P],m[P],R,p,O);I!==P&&(V>=B&&V<=B+Z||V+N>=B&&V+N<=B+Z)&&($.includes(P)||($.push(I),$.push(P),$=r(r([],$,!0),[I,P],!1),D(P)))})};return D(i),Array.from(new Set($.sort()))},M=function(i,s,c,m,R,p){m===void 0&&(m=.1),R===void 0&&(R=" - "),p===void 0&&(p=function(P){return P});var O=(0,e.getStepDecimals)(m),$=(0,n.useState)({}),D=$[0],I=$[1],N=(0,n.useState)(p(s[c].toFixed(O))),V=N[0],q=N[1];return(0,n.useEffect)(function(){if(i){var P=i.getThumbs();if(P.length<1)return;var B={},Z=i.getOffsets(),re=C(c,Z,P,s,R,O,p),le=p(s[c].toFixed(O));if(re.length){var Q=re.reduce(function(ee,oe,ge,pe){return ee.length?r(r([],ee,!0),[Z[pe[ge]].x],!1):[Z[pe[ge]].x]},[]);if(Math.min.apply(Math,Q)===Z[c].x){var de=[];re.forEach(function(ee){de.push(s[ee].toFixed(O))}),le=Array.from(new Set(de.sort(function(ee,oe){return parseFloat(ee)-parseFloat(oe)}))).map(p).join(R);var fe=Math.min.apply(Math,Q),he=Math.max.apply(Math,Q),Me=P[re[Q.indexOf(he)]].getBoundingClientRect().width;B.left="".concat(Math.abs(fe-(he+Me))/2,"px"),B.transform="translate(-50%, 0)"}else B.visibility="hidden"}q(le),I(B)}},[i,s]),[V,D]};e.useThumbOverlap=M;function L(i,s,c,m){var R=i.getBoundingClientRect(),p=R.left,O=R.top,$=R.width,D=R.height;return F(m)?Math.abs(c-(O+D/2)):Math.abs(s-(p+$/2))}var H=function(){var i,s=((i=navigator.userAgentData)===null||i===void 0?void 0:i.platform)||navigator.platform;return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(s)||navigator.userAgent.includes("Mac")&&"ontouchend"in document};e.isIOS=H}(ue)),ue}var ze;function It(){if(ze)return U;ze=1;var e=U&&U.__extends||function(){var S=function(y,d){return S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,a){t.__proto__=a}||function(t,a){for(var l in a)Object.prototype.hasOwnProperty.call(a,l)&&(t[l]=a[l])},S(y,d)};return function(y,d){if(typeof d!="function"&&d!==null)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");S(y,d);function t(){this.constructor=y}y.prototype=d===null?Object.create(d):(t.prototype=d.prototype,new t)}}(),r=U&&U.__createBinding||(Object.create?function(S,y,d,t){t===void 0&&(t=d);var a=Object.getOwnPropertyDescriptor(y,d);(!a||("get"in a?!y.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return y[d]}}),Object.defineProperty(S,t,a)}:function(S,y,d,t){t===void 0&&(t=d),S[t]=y[d]}),n=U&&U.__setModuleDefault||(Object.create?function(S,y){Object.defineProperty(S,"default",{enumerable:!0,value:y})}:function(S,y){S.default=y}),o=U&&U.__importStar||function(S){if(S&&S.__esModule)return S;var y={};if(S!=null)for(var d in S)d!=="default"&&Object.prototype.hasOwnProperty.call(S,d)&&r(y,S,d);return n(y,S),y},g=U&&U.__spreadArray||function(S,y,d){if(d||arguments.length===2)for(var t=0,a=y.length,l;t<a;t++)(l||!(t in y))&&(l||(l=Array.prototype.slice.call(y,0,t)),l[t]=y[t]);return S.concat(l||Array.prototype.slice.call(y))};Object.defineProperty(U,"__esModule",{value:!0});var _=o(We()),u=Xe(),h=$e(),j=["ArrowRight","ArrowUp","k","PageUp"],F=["ArrowLeft","ArrowDown","j","PageDown"],A=function(S){e(y,S);function y(d){var t=S.call(this,d)||this;if(t.trackRef=_.createRef(),t.thumbRefs=[],t.state={draggedTrackPos:[-1,-1],draggedThumbIndex:-1,thumbZIndexes:new Array(t.props.values.length).fill(0).map(function(a,l){return l}),isChanged:!1,markOffsets:[]},t.getOffsets=function(){var a=t.props,l=a.direction,v=a.values,f=a.min,b=a.max,E=t.trackRef.current;if(!E)return console.warn("No track element found."),[];var k=E.getBoundingClientRect(),T=(0,u.getPaddingAndBorder)(E);return t.getThumbs().map(function(w,C){var M={x:0,y:0},L=w.getBoundingClientRect(),H=(0,u.getMargin)(w);switch(l){case h.Direction.Right:return M.x=(H.left+T.left)*-1,M.y=((L.height-k.height)/2+T.top)*-1,M.x+=k.width*(0,u.relativeValue)(v[C],f,b)-L.width/2,M;case h.Direction.Left:return M.x=(H.right+T.right)*-1,M.y=((L.height-k.height)/2+T.top)*-1,M.x+=k.width-k.width*(0,u.relativeValue)(v[C],f,b)-L.width/2,M;case h.Direction.Up:return M.x=((L.width-k.width)/2+H.left+T.left)*-1,M.y=-T.left,M.y+=k.height-k.height*(0,u.relativeValue)(v[C],f,b)-L.height/2,M;case h.Direction.Down:return M.x=((L.width-k.width)/2+H.left+T.left)*-1,M.y=-T.left,M.y+=k.height*(0,u.relativeValue)(v[C],f,b)-L.height/2,M;default:return(0,u.assertUnreachable)(l)}})},t.getThumbs=function(){return t.trackRef&&t.trackRef.current?Array.from(t.trackRef.current.children).filter(function(a){return a.hasAttribute("aria-valuenow")}):(console.warn("No thumbs found in the track container. Did you forget to pass & spread the `props` param in renderTrack?"),[])},t.getTargetIndex=function(a){return t.getThumbs().findIndex(function(l){return l===a.target||l.contains(a.target)})},t.addTouchEvents=function(a){document.addEventListener("touchmove",t.schdOnTouchMove,{passive:!1}),document.addEventListener("touchend",t.schdOnEnd,{passive:!1}),document.addEventListener("touchcancel",t.schdOnEnd,{passive:!1})},t.addMouseEvents=function(a){document.addEventListener("mousemove",t.schdOnMouseMove),document.addEventListener("mouseup",t.schdOnEnd)},t.onMouseDownTrack=function(a){var l;if(!(a.button!==0||(0,u.isIOS)()))if(a.persist(),a.preventDefault(),t.addMouseEvents(a.nativeEvent),t.props.values.length>1&&t.props.draggableTrack){if(t.thumbRefs.some(function(f){var b;return(b=f.current)===null||b===void 0?void 0:b.contains(a.target)}))return;t.setState({draggedTrackPos:[a.clientX,a.clientY]},function(){return t.onMove(a.clientX,a.clientY)})}else{var v=(0,u.getClosestThumbIndex)(t.thumbRefs.map(function(f){return f.current}),a.clientX,a.clientY,t.props.direction);(l=t.thumbRefs[v].current)===null||l===void 0||l.focus(),t.setState({draggedThumbIndex:v},function(){return t.onMove(a.clientX,a.clientY)})}},t.onResize=function(){(0,u.translateThumbs)(t.getThumbs(),t.getOffsets(),t.props.rtl),t.calculateMarkOffsets()},t.onTouchStartTrack=function(a){var l;if(a.persist(),t.addTouchEvents(a.nativeEvent),t.props.values.length>1&&t.props.draggableTrack){if(t.thumbRefs.some(function(f){var b;return(b=f.current)===null||b===void 0?void 0:b.contains(a.target)}))return;t.setState({draggedTrackPos:[a.touches[0].clientX,a.touches[0].clientY]},function(){return t.onMove(a.touches[0].clientX,a.touches[0].clientY)})}else{var v=(0,u.getClosestThumbIndex)(t.thumbRefs.map(function(f){return f.current}),a.touches[0].clientX,a.touches[0].clientY,t.props.direction);(l=t.thumbRefs[v].current)===null||l===void 0||l.focus(),t.setState({draggedThumbIndex:v},function(){return t.onMove(a.touches[0].clientX,a.touches[0].clientY)})}},t.onMouseOrTouchStart=function(a){if(!t.props.disabled){var l=(0,u.isTouchEvent)(a);if(!(!l&&a.button!==0)){var v=t.getTargetIndex(a);v!==-1&&(l?t.addTouchEvents(a):t.addMouseEvents(a),t.setState({draggedThumbIndex:v,thumbZIndexes:t.state.thumbZIndexes.map(function(f,b){return b===v?Math.max.apply(Math,t.state.thumbZIndexes):f<=t.state.thumbZIndexes[v]?f:f-1})}))}}},t.onMouseMove=function(a){a.preventDefault(),t.onMove(a.clientX,a.clientY)},t.onTouchMove=function(a){a.preventDefault(),t.onMove(a.touches[0].clientX,a.touches[0].clientY)},t.onKeyDown=function(a){var l=t.props,v=l.values,f=l.onChange,b=l.step,E=l.rtl,k=l.direction,T=t.state.isChanged,w=t.getTargetIndex(a.nativeEvent),C=E||k===h.Direction.Left||k===h.Direction.Down?-1:1;w!==-1&&(j.includes(a.key)?(a.preventDefault(),t.setState({draggedThumbIndex:w,isChanged:!0}),f((0,u.replaceAt)(v,w,t.normalizeValue(v[w]+C*(a.key==="PageUp"?b*10:b),w)))):F.includes(a.key)?(a.preventDefault(),t.setState({draggedThumbIndex:w,isChanged:!0}),f((0,u.replaceAt)(v,w,t.normalizeValue(v[w]-C*(a.key==="PageDown"?b*10:b),w)))):a.key==="Tab"?t.setState({draggedThumbIndex:-1},function(){T&&t.fireOnFinalChange()}):T&&t.fireOnFinalChange())},t.onKeyUp=function(a){var l=t.state.isChanged;t.setState({draggedThumbIndex:-1},function(){l&&t.fireOnFinalChange()})},t.onMove=function(a,l){var v=t.state,f=v.draggedThumbIndex,b=v.draggedTrackPos,E=t.props,k=E.direction,T=E.min,w=E.max,C=E.onChange,M=E.values,L=E.step,H=E.rtl;if(f===-1&&b[0]===-1&&b[1]===-1)return null;var i=t.trackRef.current;if(!i)return null;var s=i.getBoundingClientRect(),c=(0,u.isVertical)(k)?s.height:s.width;if(b[0]!==-1&&b[1]!==-1){var m=a-b[0],R=l-b[1],p=0;switch(k){case h.Direction.Right:case h.Direction.Left:p=m/c*(w-T);break;case h.Direction.Down:case h.Direction.Up:p=R/c*(w-T);break;default:(0,u.assertUnreachable)(k)}if(H&&(p*=-1),Math.abs(p)>=L/2){for(var O=0;O<t.thumbRefs.length;O++){if(M[O]===w&&Math.sign(p)===1||M[O]===T&&Math.sign(p)===-1)return;var $=M[O]+p;$>w?p=w-M[O]:$<T&&(p=T-M[O])}for(var D=M.slice(0),O=0;O<t.thumbRefs.length;O++)D=(0,u.replaceAt)(D,O,t.normalizeValue(M[O]+p,O));t.setState({draggedTrackPos:[a,l]}),C(D)}}else{var I=0;switch(k){case h.Direction.Right:I=(a-s.left)/c*(w-T)+T;break;case h.Direction.Left:I=(c-(a-s.left))/c*(w-T)+T;break;case h.Direction.Down:I=(l-s.top)/c*(w-T)+T;break;case h.Direction.Up:I=(c-(l-s.top))/c*(w-T)+T;break;default:(0,u.assertUnreachable)(k)}H&&(I=w+T-I),Math.abs(M[f]-I)>=L/2&&C((0,u.replaceAt)(M,f,t.normalizeValue(I,f)))}},t.normalizeValue=function(a,l){var v=t.props,f=v.min,b=v.max,E=v.step,k=v.allowOverlap,T=v.values;return(0,u.normalizeValue)(a,l,f,b,E,k,T)},t.onEnd=function(a){if(a.preventDefault(),document.removeEventListener("mousemove",t.schdOnMouseMove),document.removeEventListener("touchmove",t.schdOnTouchMove),document.removeEventListener("mouseup",t.schdOnEnd),document.removeEventListener("touchend",t.schdOnEnd),document.removeEventListener("touchcancel",t.schdOnEnd),t.state.draggedThumbIndex===-1&&t.state.draggedTrackPos[0]===-1&&t.state.draggedTrackPos[1]===-1)return null;t.setState({draggedThumbIndex:-1,draggedTrackPos:[-1,-1]},function(){t.fireOnFinalChange()})},t.fireOnFinalChange=function(){t.setState({isChanged:!1});var a=t.props,l=a.onFinalChange,v=a.values;l&&l(v)},t.updateMarkRefs=function(a){if(!a.renderMark){t.numOfMarks=void 0,t.markRefs=void 0;return}t.numOfMarks=(a.max-a.min)/t.props.step,t.markRefs=[];for(var l=0;l<t.numOfMarks+1;l++)t.markRefs[l]=_.createRef()},t.calculateMarkOffsets=function(){if(!(!t.props.renderMark||!t.trackRef||!t.numOfMarks||!t.markRefs||t.trackRef.current===null)){for(var a=window.getComputedStyle(t.trackRef.current),l=parseInt(a.width,10),v=parseInt(a.height,10),f=parseInt(a.paddingLeft,10),b=parseInt(a.paddingTop,10),E=[],k=0;k<t.numOfMarks+1;k++){var T=9999,w=9999;if(t.markRefs[k].current){var C=t.markRefs[k].current.getBoundingClientRect();T=C.height,w=C.width}t.props.direction===h.Direction.Left||t.props.direction===h.Direction.Right?E.push([Math.round(l/t.numOfMarks*k+f-w/2),-Math.round((T-v)/2)]):E.push([Math.round(v/t.numOfMarks*k+b-T/2),-Math.round((w-l)/2)])}t.setState({markOffsets:E})}},d.step===0)throw new Error('"step" property should be a positive number');return t.schdOnMouseMove=(0,u.schd)(t.onMouseMove),t.schdOnTouchMove=(0,u.schd)(t.onTouchMove),t.schdOnEnd=(0,u.schd)(t.onEnd),t.thumbRefs=d.values.map(function(){return _.createRef()}),t.updateMarkRefs(d),t}return y.prototype.componentDidMount=function(){var d=this,t=this.props,a=t.values,l=t.min,v=t.step;this.resizeObserver=window.ResizeObserver?new window.ResizeObserver(this.onResize):{observe:function(){return window.addEventListener("resize",d.onResize)},unobserve:function(){return window.removeEventListener("resize",d.onResize)}},document.addEventListener("touchstart",this.onMouseOrTouchStart,{passive:!1}),document.addEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),!this.props.allowOverlap&&(0,u.checkInitialOverlap)(this.props.values),this.props.values.forEach(function(f){return(0,u.checkBoundaries)(f,d.props.min,d.props.max)}),this.resizeObserver.observe(this.trackRef.current),(0,u.translateThumbs)(this.getThumbs(),this.getOffsets(),this.props.rtl),this.calculateMarkOffsets(),a.forEach(function(f){(0,u.isStepDivisible)(l,f,v)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")})},y.prototype.componentDidUpdate=function(d,t){var a=this.props,l=a.max,v=a.min,f=a.step,b=a.values,E=a.rtl;(d.max!==l||d.min!==v||d.step!==f)&&this.updateMarkRefs(this.props),(0,u.translateThumbs)(this.getThumbs(),this.getOffsets(),E),(d.max!==l||d.min!==v||d.step!==f||t.markOffsets.length!==this.state.markOffsets.length)&&(this.calculateMarkOffsets(),b.forEach(function(k){(0,u.isStepDivisible)(v,k,f)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")}))},y.prototype.componentWillUnmount=function(){var d={passive:!1};document.removeEventListener("mousedown",this.onMouseOrTouchStart,d),document.removeEventListener("mousemove",this.schdOnMouseMove),document.removeEventListener("touchmove",this.schdOnTouchMove),document.removeEventListener("touchstart",this.onMouseOrTouchStart),document.removeEventListener("mouseup",this.schdOnEnd),document.removeEventListener("touchend",this.schdOnEnd),this.resizeObserver.unobserve(this.trackRef.current)},y.prototype.render=function(){var d=this,t=this.props,a=t.label,l=t.labelledBy,v=t.renderTrack,f=t.renderThumb,b=t.renderMark,E=b===void 0?function(){return null}:b,k=t.values,T=t.min,w=t.max,C=t.allowOverlap,M=t.disabled,L=this.state,H=L.draggedThumbIndex,i=L.thumbZIndexes,s=L.markOffsets;return v({props:{style:{transform:"scale(1)",cursor:H>-1?"grabbing":this.props.draggableTrack?(0,u.isVertical)(this.props.direction)?"ns-resize":"ew-resize":k.length===1&&!M?"pointer":"inherit"},onMouseDown:M?u.voidFn:this.onMouseDownTrack,onTouchStart:M?u.voidFn:this.onTouchStartTrack,ref:this.trackRef},isDragged:this.state.draggedThumbIndex>-1,disabled:M,children:g(g([],s.map(function(c,m,R){return E({props:{style:d.props.direction===h.Direction.Left||d.props.direction===h.Direction.Right?{position:"absolute",left:"".concat(c[0],"px"),marginTop:"".concat(c[1],"px")}:{position:"absolute",top:"".concat(c[0],"px"),marginLeft:"".concat(c[1],"px")},key:"mark".concat(m),ref:d.markRefs[m]},index:m})}),!0),k.map(function(c,m){var R=d.state.draggedThumbIndex===m;return f({index:m,value:c,isDragged:R,props:{style:{position:"absolute",zIndex:i[m],cursor:M?"inherit":R?"grabbing":"grab",userSelect:"none",touchAction:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none"},key:m,tabIndex:M?void 0:0,"aria-valuemax":C?w:k[m+1]||w,"aria-valuemin":C?T:k[m-1]||T,"aria-valuenow":c,draggable:!1,ref:d.thumbRefs[m],"aria-label":a,"aria-labelledby":l,role:"slider",onKeyDown:M?u.voidFn:d.onKeyDown,onKeyUp:M?u.voidFn:d.onKeyUp}})}),!0)})},y.defaultProps={label:"Accessibility label",labelledBy:null,step:1,direction:h.Direction.Right,rtl:!1,disabled:!1,allowOverlap:!1,draggableTrack:!1,min:0,max:100},y}(_.Component);return U.default=A,U}var Ne;function xt(){return Ne||(Ne=1,function(e){var r=se&&se.__importDefault||function(_){return _&&_.__esModule?_:{default:_}};Object.defineProperty(e,"__esModule",{value:!0}),e.checkValuesAgainstBoundaries=e.relativeValue=e.useThumbOverlap=e.Direction=e.getTrackBackground=e.Range=void 0;var n=r(It());e.Range=n.default;var o=Xe();Object.defineProperty(e,"getTrackBackground",{enumerable:!0,get:function(){return o.getTrackBackground}}),Object.defineProperty(e,"useThumbOverlap",{enumerable:!0,get:function(){return o.useThumbOverlap}}),Object.defineProperty(e,"relativeValue",{enumerable:!0,get:function(){return o.relativeValue}}),Object.defineProperty(e,"checkValuesAgainstBoundaries",{enumerable:!0,get:function(){return o.checkValuesAgainstBoundaries}});var g=$e();Object.defineProperty(e,"Direction",{enumerable:!0,get:function(){return g.Direction}})}(se)),se}var Ye=xt();function Ue(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(g){return Object.getOwnPropertyDescriptor(e,g).enumerable})),n.push.apply(n,o)}return n}function ve(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?Ue(Object(n),!0).forEach(function(o){Ct(e,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ue(Object(n)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(n,o))})}return e}function Ct(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var me=J("div",{position:"relative",width:"100%"});me.displayName="Root";me.displayName="Root";me.displayName="StyledRoot";var be=J("div",function(e){var r=e.$theme,n=e.$value,o=n===void 0?[]:n,g=e.$disabled,_=e.$isDragged,u=r.sizing,h="inherit";return g?h="not-allowed":_?h="grabbing":o.length===1&&(h="pointer"),{paddingTop:u.scale600,paddingBottom:u.scale600,paddingRight:u.scale600,paddingLeft:u.scale600,display:"flex",cursor:h,backgroundColor:r.colors.sliderTrackFill}});be.displayName="Track";be.displayName="Track";be.displayName="StyledTrack";var ye=J("div",function(e){var r=e.$theme,n=e.$value,o=n===void 0?[]:n,g=e.$min,_=e.$max,u=e.$disabled,h=r.colors,j=r.borders,F=r.direction,A=r.borders.useRoundedCorners?j.radius100:0;return{borderTopLeftRadius:A,borderTopRightRadius:A,borderBottomRightRadius:A,borderBottomLeftRadius:A,background:Ye.getTrackBackground({values:o,colors:o.length===1?[u?h.borderOpaque:h.primary,u?h.backgroundSecondary:h.borderOpaque]:[u?h.backgroundSecondary:h.borderOpaque,u?h.borderOpaque:h.primary,u?h.backgroundSecondary:h.borderOpaque],min:g||0,max:_||0,rtl:F==="rtl"}),height:"2px",width:"100%",alignSelf:"center",cursor:u?"not-allowed":"inherit"}});ye.displayName="InnerTrack";ye.displayName="InnerTrack";ye.displayName="StyledInnerTrack";var Te=J("div",function(e){return{width:"4px",height:"2px",backgroundColor:e.$theme.colors.backgroundPrimary,marginLeft:"16px"}});Te.displayName="Mark";Te.displayName="Mark";Te.displayName="StyledMark";var Oe=J("div",function(e){return ve(ve({},e.$theme.typography.font200),{},{color:e.$theme.colors.contentPrimary})});Oe.displayName="Tick";Oe.displayName="Tick";Oe.displayName="StyledTick";var ke=J("div",function(e){var r=e.$theme,n=r.sizing;return{display:"flex",justifyContent:"space-between",alignItems:"center",paddingRight:n.scale600,paddingLeft:n.scale600,paddingBottom:n.scale400}});ke.displayName="TickBar";ke.displayName="TickBar";ke.displayName="StyledTickBar";var we=J("div",function(e){var r=e.$theme,n=e.$value,o=n===void 0?[]:n,g=e.$thumbIndex,_=e.$disabled,u=o.length===2&&g===0,h=o.length===2&&g===1;return r.direction==="rtl"&&(h||u)&&(u=!u,h=!h),{height:"24px",width:"24px",borderTopLeftRadius:"24px",borderTopRightRadius:"24px",borderBottomLeftRadius:"24px",borderBottomRightRadius:"24px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:_?r.colors.sliderHandleFillDisabled:r.colors.sliderHandleFill,outline:"none",boxShadow:e.$isFocusVisible?"0 0 0 3px ".concat(r.colors.accent):"0 1px 4px rgba(0, 0, 0, 0.12)",cursor:_?"not-allowed":"inherit"}});we.displayName="Thumb";we.displayName="Thumb";we.displayName="StyledThumb";var Re=J("div",function(e){var r=e.$disabled,n=e.$theme;return{position:"absolute",top:"-16px",width:"4px",height:"20px",backgroundColor:r?n.colors.sliderHandleFillDisabled:n.colors.sliderHandleInnerFill}});Re.displayName="InnerThumb";Re.displayName="InnerThumb";Re.displayName="StyledInnerThumb";var Se=J("div",function(e){var r=e.$disabled,n=e.$theme;return ve(ve({position:"absolute",top:"-".concat(n.sizing.scale1400)},n.typography.font200),{},{backgroundColor:r?n.colors.sliderHandleFillDisabled:n.colors.sliderHandleInnerFill,color:n.colors.contentInversePrimary,paddingLeft:n.sizing.scale600,paddingRight:n.sizing.scale600,paddingTop:n.sizing.scale500,paddingBottom:n.sizing.scale500,borderBottomLeftRadius:"48px",borderBottomRightRadius:"48px",borderTopLeftRadius:"48px",borderTopRightRadius:"48px",whiteSpace:"nowrap"})});Se.displayName="ThumbValue";Se.displayName="ThumbValue";Se.displayName="StyledThumbValue";function He(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(g){return Object.getOwnPropertyDescriptor(e,g).enumerable})),n.push.apply(n,o)}return n}function Dt(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?He(Object(n),!0).forEach(function(o){Pt(e,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):He(Object(n)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(n,o))})}return e}function Pt(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}function X(){return X=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},X.apply(this,arguments)}function K(e,r){return Ft(e)||Lt(e,r)||At(e,r)||Bt()}function Bt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function At(e,r){if(e){if(typeof e=="string")return qe(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return qe(e,r)}}function qe(e,r){(r==null||r>e.length)&&(r=e.length);for(var n=0,o=new Array(r);n<r;n++)o[n]=e[n];return o}function Lt(e,r){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var o=[],g=!0,_=!1,u,h;try{for(n=n.call(e);!(g=(u=n.next()).done)&&(o.push(u.value),!(r&&o.length===r));g=!0);}catch(j){_=!0,h=j}finally{try{!g&&n.return!=null&&n.return()}finally{if(_)throw h}}return o}}function Ft(e){if(Array.isArray(e))return e}var jt=function(r){if(r.length>2||r.length===0)throw new Error("the value prop represents positions of thumbs, so its length can be only one or two");return r};function Vt(e){var r=e.overrides,n=r===void 0?{}:r,o=e.disabled,g=o===void 0?!1:o,_=e.marks,u=_===void 0?!1:_,h=e.onChange,j=h===void 0?function(){}:h,F=e.onFinalChange,A=F===void 0?function(){}:F,S=e.min,y=S===void 0?0:S,d=e.max,t=d===void 0?100:d,a=e.step,l=a===void 0?1:a,v=e.persistentThumb,f=v===void 0?!1:v,b=e.valueToLabel,E=b===void 0?function(Y){return Y}:b,k=e.value,T=x.useContext(ht),w=x.useState(!1),C=K(w,2),M=C[0],L=C[1],H=x.useState(!1),i=K(H,2),s=i[0],c=i[1],m=x.useState(!1),R=K(m,2),p=R[0],O=R[1],$=x.useState(-1),D=K($,2),I=D[0],N=D[1],V=x.useCallback(function(Y){gt(Y)&&O(!0);var z=Y.target.parentNode.firstChild===Y.target?0:1;N(z)},[]),q=x.useCallback(function(Y){p!==!1&&O(!1),N(-1)},[]),P=jt(k),B={$disabled:g,$step:l,$min:y,$max:t,$marks:u,$value:P,$isFocusVisible:p},Z=G(n.Root,me),re=K(Z,2),le=re[0],Q=re[1],de=G(n.Track,be),fe=K(de,2),he=fe[0],Me=fe[1],ee=G(n.InnerTrack,ye),oe=K(ee,2),ge=oe[0],pe=oe[1],Ze=G(n.Thumb,we),Ee=K(Ze,2),Ge=Ee[0],Je=Ee[1],Qe=G(n.InnerThumb,Re),Ie=K(Qe,2),et=Ie[0],tt=Ie[1],rt=G(n.ThumbValue,Se),xe=K(rt,2),nt=xe[0],at=xe[1],it=G(n.Tick,Oe),Ce=K(it,2),De=Ce[0],Pe=Ce[1],ot=G(n.TickBar,ke),Be=K(ot,2),st=Be[0],ut=Be[1],ct=G(n.Mark,Te),Ae=K(ct,2),lt=Ae[0],dt=Ae[1];return x.createElement(le,X({"data-baseweb":"slider"},B,Q,{onFocus:vt(Q,V),onBlur:pt(Q,q)}),x.createElement(Ye.Range,X({step:l,min:y,max:t,values:P,disabled:g,onChange:function(z){return j({value:z})},onFinalChange:function(z){return A({value:z})},rtl:T.direction==="rtl",renderTrack:function(z){var te=z.props,W=z.children,ne=z.isDragged;return x.createElement(he,X({onMouseDown:te.onMouseDown,onTouchStart:te.onTouchStart,$isDragged:ne},B,Me),x.createElement(ge,X({$isDragged:ne,ref:te.ref},B,pe),W))},renderThumb:function(z){var te=z.props,W=z.index,ne=z.isDragged,Le=f||(!!W&&s||!W&&M||ne)&&!g;return x.createElement(Ge,X({},te,{onMouseEnter:function(){W===0?L(!0):c(!0)},onMouseLeave:function(){W===0?L(!1):c(!1)},$thumbIndex:W,$isDragged:ne,style:Dt({},te.style)},B,Je,{$isFocusVisible:p&&I===W}),Le&&x.createElement(nt,X({$thumbIndex:W,$isDragged:ne},B,at),E(P[W])),Le&&x.createElement(et,X({$thumbIndex:W,$isDragged:ne},B,tt)))}},u?{renderMark:function(z){var te=z.props,W=z.index;return x.createElement(lt,X({$markIndex:W},te,B,dt))}}:{})),x.createElement(st,X({},B,ut),x.createElement(De,X({},B,Pe),E(y)),x.createElement(De,X({},B,Pe),E(t))))}const zt=Ke("div",{target:"e43yrtf0"})(({disabled:e,theme:r})=>({alignItems:"center",backgroundColor:e?r.colors.gray:r.colors.primary,borderTopLeftRadius:"100%",borderTopRightRadius:"100%",borderBottomLeftRadius:"100%",borderBottomRightRadius:"100%",borderTopStyle:"none",borderBottomStyle:"none",borderRightStyle:"none",borderLeftStyle:"none",boxShadow:"none",display:"flex",justifyContent:"center",height:r.sizes.sliderThumb,width:r.sizes.sliderThumb,":focus":{outline:"none"},":focus-visible":{boxShadow:`0 0 0 0.2rem ${mt(r.colors.primary,.5)}`}})),Nt=Ke("div",{target:"e43yrtf1"})(({disabled:e,theme:r})=>({fontFamily:r.genericFonts.bodyFont,fontSize:r.fontSizes.sm,color:e?r.colors.gray:r.colors.primary,top:"-1.6em",position:"absolute",whiteSpace:"nowrap",backgroundColor:r.colors.transparent,lineHeight:r.lineHeights.base,fontWeight:r.fontWeights.normal,pointerEvents:"none"})),Ut=200;function Ht({disabled:e,element:r,widgetMgr:n,fragmentId:o}){const[g,_]=Et({getStateFromWidgetMgr:qt,getDefaultStateFromProto:Wt,getCurrStateFromProto:Kt,updateWidgetMgrState:Xt,element:r,widgetMgr:n,fragmentId:o}),[u,h]=x.useState(g),j=x.useRef(null),[F]=x.useState([]),[A]=x.useState([]),S=yt(),y=u.map(f=>Zt(f,r)),d=r.label;x.useEffect(()=>{h(g)},[g]);const t=x.useCallback(Tt(Ut,f=>{_({value:f,fromUi:!0})}),[]),a=x.useCallback(({value:f})=>{h(f),t(f)},[t]),l=x.useCallback(x.forwardRef(function(b,E){const{$thumbIndex:k}=b,T=k||0;F[T]=E,A[T]||=x.createRef();const w=Ot(b,["role","style","aria-valuemax","aria-valuemin","aria-valuenow","tabIndex","onKeyUp","onKeyDown","onMouseEnter","onMouseLeave","draggable"]),C=y[T];return ae(zt,{...w,disabled:b.$disabled===!0,ref:F[T],"aria-valuetext":C,"aria-label":d,children:ae(Nt,{"data-testid":"stSliderThumbValue",disabled:b.$disabled===!0,ref:A[T],children:C})})}),[]);x.useEffect(()=>{A.map((w,C)=>{w.current&&(w.current.innerText=y[C])}),F.map((w,C)=>{w.current&&w.current.setAttribute("aria-valuetext",y[C])});const f=j.current??null,b=F[0].current,E=F[1]?.current,k=A[0].current,T=A[1]?.current;Jt(f,b,E,k,T)});const v=x.useCallback(({$disabled:f})=>({height:S.spacing.twoXS,...f?{background:S.colors.darkenedBgMix25}:{}}),[S.colors.darkenedBgMix25,S.spacing.twoXS]);return kt("div",{ref:j,className:"stSlider","data-testid":"stSlider",children:[ae(_t,{label:r.label,disabled:e,labelVisibility:wt(r.labelVisibility?.value),children:r.help&&ae(Rt,{children:ae(St,{content:r.help,placement:Mt.TOP_RIGHT})})}),ae(Vt,{min:r.min,max:r.max,step:r.step,value:Gt(u,r),onChange:a,disabled:e,overrides:{Thumb:l,Track:{style:{backgroundColor:"none !important",paddingLeft:S.spacing.none,paddingRight:S.spacing.none,paddingTop:`calc((${S.sizes.minElementHeight} - ${S.spacing.twoXS}) / 2)`,paddingBottom:`calc((${S.sizes.minElementHeight} - ${S.spacing.twoXS}) / 2)`}},InnerTrack:{style:v},TickBar:()=>null}})]})}function qt(e,r){return e.getDoubleArrayValue(r)}function Wt(e){return e.default}function Kt(e){return e.value}function Xt(e,r,n,o){r.setDoubleArrayValue(e,n.value,{fromUi:n.fromUi},o)}function Yt(e){const{dataType:r}=e;return r===_e.DataType.DATETIME||r===_e.DataType.DATE||r===_e.DataType.TIME}function Zt(e,r){const{format:n,options:o}=r;return Yt(r)?$t.utc(e/1e3).format(n):o.length>0?Fe.sprintf(n,o[e]):Fe.sprintf(n,e)}function Gt(e,r){const{min:n,max:o}=r;let g=e[0],_=e.length>1?e[1]:e[0];return g>_&&(g=_),g<n&&(g=n),g>o&&(g=o),_<n&&(_=n),_>o&&(_=o),e.length>1?[g,_]:[g]}function Jt(e,r,n,o,g){!e||!r||!o||(ie(e,r,o),n&&g&&(ie(e,n,g),Qt(e,r,n,o,g)))}function ie(e,r,n){const o=e.getBoundingClientRect(),g=r.getBoundingClientRect(),_=n.getBoundingClientRect(),u=g.left+g.width/2,h=u-_.width/2<o.left,j=u+_.width/2>o.right;n.style.left=h?"0":"",n.style.right=j?"0":""}function Qt(e,r,n,o,g){const u=e.getBoundingClientRect(),h=r.getBoundingClientRect(),j=n.getBoundingClientRect(),F=o.getBoundingClientRect(),A=g.getBoundingClientRect(),S=u.left+u.width/2,y=h.left+h.width/2,d=j.left+j.width/2,t=y-F.width/2>=u.left,a=d+A.width/2<=u.right,l=h.left-F.width>=u.left,v=j.right+A.width<=u.right,f=t?F.width/2:F.width,b=a?A.width/2:A.width,E=y+f;if(d-b-E>24){ie(e,r,o),ie(e,n,g);return}if(l&&v){o.style.left="",o.style.right=`${Math.round(h.width)}px`,g.style.left=`${Math.round(j.width)}px`,g.style.right="";return}y<S?(ie(e,r,o),g.style.left=`${Math.round(y+f+24-d)}px`,g.style.right=""):(ie(e,n,g),o.style.left="",o.style.right=`${-Math.round(d-b-24-y)}px`)}const ar=bt(x.memo(Ht));export{ar as default};
