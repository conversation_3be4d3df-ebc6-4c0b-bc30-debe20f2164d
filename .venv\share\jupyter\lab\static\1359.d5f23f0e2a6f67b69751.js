"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[1359],{41359:(e,t,s)=>{s.d(t,{Lh:()=>m,NM:()=>b,_$:()=>o,tM:()=>f});var i=s(15051);var n=s(94065);var a=s(96049);var r=s(75905);var u=s(24982);var l=function(){var e=(0,r.K2)((function(e,t,s,i){for(s=s||{},i=e.length;i--;s[e[i]]=t);return s}),"o"),t=[1,18],s=[1,19],i=[1,20],n=[1,41],a=[1,42],u=[1,26],l=[1,24],o=[1,25],c=[1,32],h=[1,33],p=[1,34],d=[1,45],A=[1,35],b=[1,36],y=[1,37],f=[1,38],k=[1,27],C=[1,28],g=[1,29],m=[1,30],E=[1,31],T=[1,44],D=[1,46],F=[1,43],B=[1,47],_=[1,9],S=[1,8,9],N=[1,58],L=[1,59],$=[1,60],v=[1,61],x=[1,62],O=[1,63],I=[1,64],w=[1,8,9,41],R=[1,76],P=[1,8,9,12,13,22,39,41,44,66,67,68,69,70,71,72,77,79],K=[1,8,9,12,13,17,20,22,39,41,44,48,58,66,67,68,69,70,71,72,77,79,84,99,101,102],M=[13,58,84,99,101,102],G=[13,58,71,72,84,99,101,102],U=[13,58,66,67,68,69,70,84,99,101,102],Y=[1,98],z=[1,115],Q=[1,107],j=[1,113],W=[1,108],X=[1,109],V=[1,110],q=[1,111],H=[1,112],J=[1,114],Z=[22,58,59,80,84,85,86,87,88,89],ee=[1,8,9,39,41,44],te=[1,8,9,22],se=[1,143],ie=[1,8,9,59],ne=[1,8,9,22,58,59,80,84,85,86,87,88,89];var ae={trace:(0,r.K2)((function e(){}),"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,DOT:17,className:18,classLiteralName:19,GENERICTYPE:20,relationStatement:21,LABEL:22,namespaceStatement:23,classStatement:24,memberStatement:25,annotationStatement:26,clickStatement:27,styleStatement:28,cssClassStatement:29,noteStatement:30,classDefStatement:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,namespaceIdentifier:38,STRUCT_START:39,classStatements:40,STRUCT_STOP:41,NAMESPACE:42,classIdentifier:43,STYLE_SEPARATOR:44,members:45,CLASS:46,ANNOTATION_START:47,ANNOTATION_END:48,MEMBER:49,SEPARATOR:50,relation:51,NOTE_FOR:52,noteText:53,NOTE:54,CLASSDEF:55,classList:56,stylesOpt:57,ALPHA:58,COMMA:59,direction_tb:60,direction_bt:61,direction_rl:62,direction_lr:63,relationType:64,lineType:65,AGGREGATION:66,EXTENSION:67,COMPOSITION:68,DEPENDENCY:69,LOLLIPOP:70,LINE:71,DOTTED_LINE:72,CALLBACK:73,LINK:74,LINK_TARGET:75,CLICK:76,CALLBACK_NAME:77,CALLBACK_ARGS:78,HREF:79,STYLE:80,CSSCLASS:81,style:82,styleComponent:83,NUM:84,COLON:85,UNIT:86,SPACE:87,BRKT:88,PCT:89,commentToken:90,textToken:91,graphCodeTokens:92,textNoTagsToken:93,TAGSTART:94,TAGEND:95,"==":96,"--":97,DEFAULT:98,MINUS:99,keywords:100,UNICODE_TEXT:101,BQUOTE_STR:102,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",17:"DOT",20:"GENERICTYPE",22:"LABEL",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",39:"STRUCT_START",41:"STRUCT_STOP",42:"NAMESPACE",44:"STYLE_SEPARATOR",46:"CLASS",47:"ANNOTATION_START",48:"ANNOTATION_END",49:"MEMBER",50:"SEPARATOR",52:"NOTE_FOR",54:"NOTE",55:"CLASSDEF",58:"ALPHA",59:"COMMA",60:"direction_tb",61:"direction_bt",62:"direction_rl",63:"direction_lr",66:"AGGREGATION",67:"EXTENSION",68:"COMPOSITION",69:"DEPENDENCY",70:"LOLLIPOP",71:"LINE",72:"DOTTED_LINE",73:"CALLBACK",74:"LINK",75:"LINK_TARGET",76:"CLICK",77:"CALLBACK_NAME",78:"CALLBACK_ARGS",79:"HREF",80:"STYLE",81:"CSSCLASS",84:"NUM",85:"COLON",86:"UNIT",87:"SPACE",88:"BRKT",89:"PCT",92:"graphCodeTokens",94:"TAGSTART",95:"TAGEND",96:"==",97:"--",98:"DEFAULT",99:"MINUS",100:"keywords",101:"UNICODE_TEXT",102:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,3],[15,2],[18,1],[18,3],[18,1],[18,2],[18,2],[18,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[23,4],[23,5],[38,2],[40,1],[40,2],[40,3],[24,1],[24,3],[24,4],[24,6],[43,2],[43,3],[26,4],[45,1],[45,2],[25,1],[25,2],[25,1],[25,1],[21,3],[21,4],[21,4],[21,5],[30,3],[30,2],[31,3],[56,1],[56,3],[32,1],[32,1],[32,1],[32,1],[51,3],[51,2],[51,2],[51,1],[64,1],[64,1],[64,1],[64,1],[64,1],[65,1],[65,1],[27,3],[27,4],[27,3],[27,4],[27,4],[27,5],[27,3],[27,4],[27,4],[27,5],[27,4],[27,5],[27,5],[27,6],[28,3],[29,3],[57,1],[57,3],[82,1],[82,2],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[90,1],[90,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[93,1],[93,1],[93,1],[93,1],[16,1],[16,1],[16,1],[16,1],[19,1],[53,1]],performAction:(0,r.K2)((function e(t,s,i,n,a,r,u){var l=r.length-1;switch(a){case 8:this.$=r[l-1];break;case 9:case 12:case 14:this.$=r[l];break;case 10:case 13:this.$=r[l-2]+"."+r[l];break;case 11:case 15:this.$=r[l-1]+r[l];break;case 16:case 17:this.$=r[l-1]+"~"+r[l]+"~";break;case 18:n.addRelation(r[l]);break;case 19:r[l-1].title=n.cleanupLabel(r[l]);n.addRelation(r[l-1]);break;case 30:this.$=r[l].trim();n.setAccTitle(this.$);break;case 31:case 32:this.$=r[l].trim();n.setAccDescription(this.$);break;case 33:n.addClassesToNamespace(r[l-3],r[l-1]);break;case 34:n.addClassesToNamespace(r[l-4],r[l-1]);break;case 35:this.$=r[l];n.addNamespace(r[l]);break;case 36:this.$=[r[l]];break;case 37:this.$=[r[l-1]];break;case 38:r[l].unshift(r[l-2]);this.$=r[l];break;case 40:n.setCssClass(r[l-2],r[l]);break;case 41:n.addMembers(r[l-3],r[l-1]);break;case 42:n.setCssClass(r[l-5],r[l-3]);n.addMembers(r[l-5],r[l-1]);break;case 43:this.$=r[l];n.addClass(r[l]);break;case 44:this.$=r[l-1];n.addClass(r[l-1]);n.setClassLabel(r[l-1],r[l]);break;case 45:n.addAnnotation(r[l],r[l-2]);break;case 46:case 59:this.$=[r[l]];break;case 47:r[l].push(r[l-1]);this.$=r[l];break;case 48:break;case 49:n.addMember(r[l-1],n.cleanupLabel(r[l]));break;case 50:break;case 51:break;case 52:this.$={id1:r[l-2],id2:r[l],relation:r[l-1],relationTitle1:"none",relationTitle2:"none"};break;case 53:this.$={id1:r[l-3],id2:r[l],relation:r[l-1],relationTitle1:r[l-2],relationTitle2:"none"};break;case 54:this.$={id1:r[l-3],id2:r[l],relation:r[l-2],relationTitle1:"none",relationTitle2:r[l-1]};break;case 55:this.$={id1:r[l-4],id2:r[l],relation:r[l-2],relationTitle1:r[l-3],relationTitle2:r[l-1]};break;case 56:n.addNote(r[l],r[l-1]);break;case 57:n.addNote(r[l]);break;case 58:this.$=r[l-2];n.defineClass(r[l-1],r[l]);break;case 60:this.$=r[l-2].concat([r[l]]);break;case 61:n.setDirection("TB");break;case 62:n.setDirection("BT");break;case 63:n.setDirection("RL");break;case 64:n.setDirection("LR");break;case 65:this.$={type1:r[l-2],type2:r[l],lineType:r[l-1]};break;case 66:this.$={type1:"none",type2:r[l],lineType:r[l-1]};break;case 67:this.$={type1:r[l-1],type2:"none",lineType:r[l]};break;case 68:this.$={type1:"none",type2:"none",lineType:r[l]};break;case 69:this.$=n.relationType.AGGREGATION;break;case 70:this.$=n.relationType.EXTENSION;break;case 71:this.$=n.relationType.COMPOSITION;break;case 72:this.$=n.relationType.DEPENDENCY;break;case 73:this.$=n.relationType.LOLLIPOP;break;case 74:this.$=n.lineType.LINE;break;case 75:this.$=n.lineType.DOTTED_LINE;break;case 76:case 82:this.$=r[l-2];n.setClickEvent(r[l-1],r[l]);break;case 77:case 83:this.$=r[l-3];n.setClickEvent(r[l-2],r[l-1]);n.setTooltip(r[l-2],r[l]);break;case 78:this.$=r[l-2];n.setLink(r[l-1],r[l]);break;case 79:this.$=r[l-3];n.setLink(r[l-2],r[l-1],r[l]);break;case 80:this.$=r[l-3];n.setLink(r[l-2],r[l-1]);n.setTooltip(r[l-2],r[l]);break;case 81:this.$=r[l-4];n.setLink(r[l-3],r[l-2],r[l]);n.setTooltip(r[l-3],r[l-1]);break;case 84:this.$=r[l-3];n.setClickEvent(r[l-2],r[l-1],r[l]);break;case 85:this.$=r[l-4];n.setClickEvent(r[l-3],r[l-2],r[l-1]);n.setTooltip(r[l-3],r[l]);break;case 86:this.$=r[l-3];n.setLink(r[l-2],r[l]);break;case 87:this.$=r[l-4];n.setLink(r[l-3],r[l-1],r[l]);break;case 88:this.$=r[l-4];n.setLink(r[l-3],r[l-1]);n.setTooltip(r[l-3],r[l]);break;case 89:this.$=r[l-5];n.setLink(r[l-4],r[l-2],r[l]);n.setTooltip(r[l-4],r[l-1]);break;case 90:this.$=r[l-2];n.setCssStyle(r[l-1],r[l]);break;case 91:n.setCssClass(r[l-1],r[l]);break;case 92:this.$=[r[l]];break;case 93:r[l-2].push(r[l]);this.$=r[l-2];break;case 95:this.$=r[l-1]+r[l];break}}),"anonymous"),table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:t,35:s,37:i,38:22,42:n,43:23,46:a,47:u,49:l,50:o,52:c,54:h,55:p,58:d,60:A,61:b,62:y,63:f,73:k,74:C,76:g,80:m,81:E,84:T,99:D,101:F,102:B},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},e(_,[2,5],{8:[1,48]}),{8:[1,49]},e(S,[2,18],{22:[1,50]}),e(S,[2,20]),e(S,[2,21]),e(S,[2,22]),e(S,[2,23]),e(S,[2,24]),e(S,[2,25]),e(S,[2,26]),e(S,[2,27]),e(S,[2,28]),e(S,[2,29]),{34:[1,51]},{36:[1,52]},e(S,[2,32]),e(S,[2,48],{51:53,64:56,65:57,13:[1,54],22:[1,55],66:N,67:L,68:$,69:v,70:x,71:O,72:I}),{39:[1,65]},e(w,[2,39],{39:[1,67],44:[1,66]}),e(S,[2,50]),e(S,[2,51]),{16:68,58:d,84:T,99:D,101:F},{16:39,18:69,19:40,58:d,84:T,99:D,101:F,102:B},{16:39,18:70,19:40,58:d,84:T,99:D,101:F,102:B},{16:39,18:71,19:40,58:d,84:T,99:D,101:F,102:B},{58:[1,72]},{13:[1,73]},{16:39,18:74,19:40,58:d,84:T,99:D,101:F,102:B},{13:R,53:75},{56:77,58:[1,78]},e(S,[2,61]),e(S,[2,62]),e(S,[2,63]),e(S,[2,64]),e(P,[2,12],{16:39,19:40,18:80,17:[1,79],20:[1,81],58:d,84:T,99:D,101:F,102:B}),e(P,[2,14],{20:[1,82]}),{15:83,16:84,58:d,84:T,99:D,101:F},{16:39,18:85,19:40,58:d,84:T,99:D,101:F,102:B},e(K,[2,118]),e(K,[2,119]),e(K,[2,120]),e(K,[2,121]),e([1,8,9,12,13,20,22,39,41,44,66,67,68,69,70,71,72,77,79],[2,122]),e(_,[2,6],{10:5,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,18:21,38:22,43:23,16:39,19:40,5:86,33:t,35:s,37:i,42:n,46:a,47:u,49:l,50:o,52:c,54:h,55:p,58:d,60:A,61:b,62:y,63:f,73:k,74:C,76:g,80:m,81:E,84:T,99:D,101:F,102:B}),{5:87,10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:t,35:s,37:i,38:22,42:n,43:23,46:a,47:u,49:l,50:o,52:c,54:h,55:p,58:d,60:A,61:b,62:y,63:f,73:k,74:C,76:g,80:m,81:E,84:T,99:D,101:F,102:B},e(S,[2,19]),e(S,[2,30]),e(S,[2,31]),{13:[1,89],16:39,18:88,19:40,58:d,84:T,99:D,101:F,102:B},{51:90,64:56,65:57,66:N,67:L,68:$,69:v,70:x,71:O,72:I},e(S,[2,49]),{65:91,71:O,72:I},e(M,[2,68],{64:92,66:N,67:L,68:$,69:v,70:x}),e(G,[2,69]),e(G,[2,70]),e(G,[2,71]),e(G,[2,72]),e(G,[2,73]),e(U,[2,74]),e(U,[2,75]),{8:[1,94],24:95,40:93,43:23,46:a},{16:96,58:d,84:T,99:D,101:F},{45:97,49:Y},{48:[1,99]},{13:[1,100]},{13:[1,101]},{77:[1,102],79:[1,103]},{22:z,57:104,58:Q,80:j,82:105,83:106,84:W,85:X,86:V,87:q,88:H,89:J},{58:[1,116]},{13:R,53:117},e(S,[2,57]),e(S,[2,123]),{22:z,57:118,58:Q,59:[1,119],80:j,82:105,83:106,84:W,85:X,86:V,87:q,88:H,89:J},e(Z,[2,59]),{16:39,18:120,19:40,58:d,84:T,99:D,101:F,102:B},e(P,[2,15]),e(P,[2,16]),e(P,[2,17]),{39:[2,35]},{15:122,16:84,17:[1,121],39:[2,9],58:d,84:T,99:D,101:F},e(ee,[2,43],{11:123,12:[1,124]}),e(_,[2,7]),{9:[1,125]},e(te,[2,52]),{16:39,18:126,19:40,58:d,84:T,99:D,101:F,102:B},{13:[1,128],16:39,18:127,19:40,58:d,84:T,99:D,101:F,102:B},e(M,[2,67],{64:129,66:N,67:L,68:$,69:v,70:x}),e(M,[2,66]),{41:[1,130]},{24:95,40:131,43:23,46:a},{8:[1,132],41:[2,36]},e(w,[2,40],{39:[1,133]}),{41:[1,134]},{41:[2,46],45:135,49:Y},{16:39,18:136,19:40,58:d,84:T,99:D,101:F,102:B},e(S,[2,76],{13:[1,137]}),e(S,[2,78],{13:[1,139],75:[1,138]}),e(S,[2,82],{13:[1,140],78:[1,141]}),{13:[1,142]},e(S,[2,90],{59:se}),e(ie,[2,92],{83:144,22:z,58:Q,80:j,84:W,85:X,86:V,87:q,88:H,89:J}),e(ne,[2,94]),e(ne,[2,96]),e(ne,[2,97]),e(ne,[2,98]),e(ne,[2,99]),e(ne,[2,100]),e(ne,[2,101]),e(ne,[2,102]),e(ne,[2,103]),e(ne,[2,104]),e(S,[2,91]),e(S,[2,56]),e(S,[2,58],{59:se}),{58:[1,145]},e(P,[2,13]),{15:146,16:84,58:d,84:T,99:D,101:F},{39:[2,11]},e(ee,[2,44]),{13:[1,147]},{1:[2,4]},e(te,[2,54]),e(te,[2,53]),{16:39,18:148,19:40,58:d,84:T,99:D,101:F,102:B},e(M,[2,65]),e(S,[2,33]),{41:[1,149]},{24:95,40:150,41:[2,37],43:23,46:a},{45:151,49:Y},e(w,[2,41]),{41:[2,47]},e(S,[2,45]),e(S,[2,77]),e(S,[2,79]),e(S,[2,80],{75:[1,152]}),e(S,[2,83]),e(S,[2,84],{13:[1,153]}),e(S,[2,86],{13:[1,155],75:[1,154]}),{22:z,58:Q,80:j,82:156,83:106,84:W,85:X,86:V,87:q,88:H,89:J},e(ne,[2,95]),e(Z,[2,60]),{39:[2,10]},{14:[1,157]},e(te,[2,55]),e(S,[2,34]),{41:[2,38]},{41:[1,158]},e(S,[2,81]),e(S,[2,85]),e(S,[2,87]),e(S,[2,88],{75:[1,159]}),e(ie,[2,93],{83:144,22:z,58:Q,80:j,84:W,85:X,86:V,87:q,88:H,89:J}),e(ee,[2,8]),e(w,[2,42]),e(S,[2,89])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],83:[2,35],122:[2,11],125:[2,4],135:[2,47],146:[2,10],150:[2,38]},parseError:(0,r.K2)((function e(t,s){if(s.recoverable){this.trace(t)}else{var i=new Error(t);i.hash=s;throw i}}),"parseError"),parse:(0,r.K2)((function e(t){var s=this,i=[0],n=[],a=[null],u=[],l=this.table,o="",c=0,h=0,p=0,d=2,A=1;var b=u.slice.call(arguments,1);var y=Object.create(this.lexer);var f={yy:{}};for(var k in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,k)){f.yy[k]=this.yy[k]}}y.setInput(t,f.yy);f.yy.lexer=y;f.yy.parser=this;if(typeof y.yylloc=="undefined"){y.yylloc={}}var C=y.yylloc;u.push(C);var g=y.options&&y.options.ranges;if(typeof f.yy.parseError==="function"){this.parseError=f.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function m(e){i.length=i.length-2*e;a.length=a.length-e;u.length=u.length-e}(0,r.K2)(m,"popStack");function E(){var e;e=n.pop()||y.lex()||A;if(typeof e!=="number"){if(e instanceof Array){n=e;e=n.pop()}e=s.symbols_[e]||e}return e}(0,r.K2)(E,"lex");var T,D,F,B,_,S,N={},L,$,v,x;while(true){F=i[i.length-1];if(this.defaultActions[F]){B=this.defaultActions[F]}else{if(T===null||typeof T=="undefined"){T=E()}B=l[F]&&l[F][T]}if(typeof B==="undefined"||!B.length||!B[0]){var O="";x=[];for(L in l[F]){if(this.terminals_[L]&&L>d){x.push("'"+this.terminals_[L]+"'")}}if(y.showPosition){O="Parse error on line "+(c+1)+":\n"+y.showPosition()+"\nExpecting "+x.join(", ")+", got '"+(this.terminals_[T]||T)+"'"}else{O="Parse error on line "+(c+1)+": Unexpected "+(T==A?"end of input":"'"+(this.terminals_[T]||T)+"'")}this.parseError(O,{text:y.match,token:this.terminals_[T]||T,line:y.yylineno,loc:C,expected:x})}if(B[0]instanceof Array&&B.length>1){throw new Error("Parse Error: multiple actions possible at state: "+F+", token: "+T)}switch(B[0]){case 1:i.push(T);a.push(y.yytext);u.push(y.yylloc);i.push(B[1]);T=null;if(!D){h=y.yyleng;o=y.yytext;c=y.yylineno;C=y.yylloc;if(p>0){p--}}else{T=D;D=null}break;case 2:$=this.productions_[B[1]][1];N.$=a[a.length-$];N._$={first_line:u[u.length-($||1)].first_line,last_line:u[u.length-1].last_line,first_column:u[u.length-($||1)].first_column,last_column:u[u.length-1].last_column};if(g){N._$.range=[u[u.length-($||1)].range[0],u[u.length-1].range[1]]}S=this.performAction.apply(N,[o,h,c,f.yy,B[1],a,u].concat(b));if(typeof S!=="undefined"){return S}if($){i=i.slice(0,-1*$*2);a=a.slice(0,-1*$);u=u.slice(0,-1*$)}i.push(this.productions_[B[1]][0]);a.push(N.$);u.push(N._$);v=l[i[i.length-2]][i[i.length-1]];i.push(v);break;case 3:return true}}return true}),"parse")};var re=function(){var e={EOF:1,parseError:(0,r.K2)((function e(t,s){if(this.yy.parser){this.yy.parser.parseError(t,s)}else{throw new Error(t)}}),"parseError"),setInput:(0,r.K2)((function(e,t){this.yy=t||this.yy||{};this._input=e;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,r.K2)((function(){var e=this._input[0];this.yytext+=e;this.yyleng++;this.offset++;this.match+=e;this.matched+=e;var t=e.match(/(?:\r\n?|\n).*/g);if(t){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return e}),"input"),unput:(0,r.K2)((function(e){var t=e.length;var s=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-t);this.offset-=t;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(s.length-1){this.yylineno-=s.length-1}var n=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-t};if(this.options.ranges){this.yylloc.range=[n[0],n[0]+this.yyleng-t]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,r.K2)((function(){this._more=true;return this}),"more"),reject:(0,r.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,r.K2)((function(e){this.unput(this.match.slice(e))}),"less"),pastInput:(0,r.K2)((function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,r.K2)((function(){var e=this.match;if(e.length<20){e+=this._input.substr(0,20-e.length)}return(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,r.K2)((function(){var e=this.pastInput();var t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"}),"showPosition"),test_match:(0,r.K2)((function(e,t){var s,i,n;if(this.options.backtrack_lexer){n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){n.yylloc.range=this.yylloc.range.slice(0)}}i=e[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length};this.yytext+=e[0];this.match+=e[0];this.matches=e;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(e[0].length);this.matched+=e[0];s=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(s){return s}else if(this._backtrack){for(var a in n){this[a]=n[a]}return false}return false}),"test_match"),next:(0,r.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var e,t,s,i;if(!this._more){this.yytext="";this.match=""}var n=this._currentRules();for(var a=0;a<n.length;a++){s=this._input.match(this.rules[n[a]]);if(s&&(!t||s[0].length>t[0].length)){t=s;i=a;if(this.options.backtrack_lexer){e=this.test_match(s,n[a]);if(e!==false){return e}else if(this._backtrack){t=false;continue}else{return false}}else if(!this.options.flex){break}}}if(t){e=this.test_match(t,n[i]);if(e!==false){return e}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,r.K2)((function e(){var t=this.next();if(t){return t}else{return this.lex()}}),"lex"),begin:(0,r.K2)((function e(t){this.conditionStack.push(t)}),"begin"),popState:(0,r.K2)((function e(){var t=this.conditionStack.length-1;if(t>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,r.K2)((function e(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,r.K2)((function e(t){t=this.conditionStack.length-1-Math.abs(t||0);if(t>=0){return this.conditionStack[t]}else{return"INITIAL"}}),"topState"),pushState:(0,r.K2)((function e(t){this.begin(t)}),"pushState"),stateStackSize:(0,r.K2)((function e(){return this.conditionStack.length}),"stateStackSize"),options:{},performAction:(0,r.K2)((function e(t,s,i,n){var a=n;switch(i){case 0:return 60;break;case 1:return 61;break;case 2:return 62;break;case 3:return 63;break;case 4:break;case 5:break;case 6:this.begin("acc_title");return 33;break;case 7:this.popState();return"acc_title_value";break;case 8:this.begin("acc_descr");return 35;break;case 9:this.popState();return"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";break;case 13:return 8;break;case 14:break;case 15:return 7;break;case 16:return 7;break;case 17:return"EDGE_STATE";break;case 18:this.begin("callback_name");break;case 19:this.popState();break;case 20:this.popState();this.begin("callback_args");break;case 21:return 77;break;case 22:this.popState();break;case 23:return 78;break;case 24:this.popState();break;case 25:return"STR";break;case 26:this.begin("string");break;case 27:return 80;break;case 28:return 55;break;case 29:this.begin("namespace");return 42;break;case 30:this.popState();return 8;break;case 31:break;case 32:this.begin("namespace-body");return 39;break;case 33:this.popState();return 41;break;case 34:return"EOF_IN_STRUCT";break;case 35:return 8;break;case 36:break;case 37:return"EDGE_STATE";break;case 38:this.begin("class");return 46;break;case 39:this.popState();return 8;break;case 40:break;case 41:this.popState();this.popState();return 41;break;case 42:this.begin("class-body");return 39;break;case 43:this.popState();return 41;break;case 44:return"EOF_IN_STRUCT";break;case 45:return"EDGE_STATE";break;case 46:return"OPEN_IN_STRUCT";break;case 47:break;case 48:return"MEMBER";break;case 49:return 81;break;case 50:return 73;break;case 51:return 74;break;case 52:return 76;break;case 53:return 52;break;case 54:return 54;break;case 55:return 47;break;case 56:return 48;break;case 57:return 79;break;case 58:this.popState();break;case 59:return"GENERICTYPE";break;case 60:this.begin("generic");break;case 61:this.popState();break;case 62:return"BQUOTE_STR";break;case 63:this.begin("bqstring");break;case 64:return 75;break;case 65:return 75;break;case 66:return 75;break;case 67:return 75;break;case 68:return 67;break;case 69:return 67;break;case 70:return 69;break;case 71:return 69;break;case 72:return 68;break;case 73:return 66;break;case 74:return 70;break;case 75:return 71;break;case 76:return 72;break;case 77:return 22;break;case 78:return 44;break;case 79:return 99;break;case 80:return 17;break;case 81:return"PLUS";break;case 82:return 85;break;case 83:return 59;break;case 84:return 88;break;case 85:return 88;break;case 86:return 89;break;case 87:return"EQUALS";break;case 88:return"EQUALS";break;case 89:return 58;break;case 90:return 12;break;case 91:return 14;break;case 92:return"PUNCTUATION";break;case 93:return 84;break;case 94:return 101;break;case 95:return 87;break;case 96:return 87;break;case 97:return 9;break}}),"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:classDef\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,33,34,35,36,37,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},namespace:{rules:[26,29,30,31,32,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},"class-body":{rules:[26,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},class:{rules:[26,39,40,41,42,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},acc_descr_multiline:{rules:[11,12,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},acc_descr:{rules:[9,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},acc_title:{rules:[7,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},callback_args:{rules:[22,23,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},callback_name:{rules:[19,20,21,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},href:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},struct:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},generic:{rules:[26,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},bqstring:{rules:[26,49,50,51,52,53,54,55,56,57,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},string:{rules:[24,25,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,29,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],inclusive:true}}};return e}();ae.lexer=re;function ue(){this.yy={}}(0,r.K2)(ue,"Parser");ue.prototype=ae;ae.Parser=ue;return new ue}();l.parser=l;var o=l;var c=["#","+","~","-",""];var h=class{static{(0,r.K2)(this,"ClassMember")}constructor(e,t){this.memberType=t;this.visibility="";this.classifier="";this.text="";const s=(0,r.jZ)(e,(0,r.D7)());this.parseMember(s)}getDisplayDetails(){let e=this.visibility+(0,r.QO)(this.id);if(this.memberType==="method"){e+=`(${(0,r.QO)(this.parameters.trim())})`;if(this.returnType){e+=" : "+(0,r.QO)(this.returnType)}}e=e.trim();const t=this.parseClassifier();return{displayText:e,cssStyle:t}}parseMember(e){let t="";if(this.memberType==="method"){const s=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/;const i=s.exec(e);if(i){const e=i[1]?i[1].trim():"";if(c.includes(e)){this.visibility=e}this.id=i[2];this.parameters=i[3]?i[3].trim():"";t=i[4]?i[4].trim():"";this.returnType=i[5]?i[5].trim():"";if(t===""){const e=this.returnType.substring(this.returnType.length-1);if(/[$*]/.exec(e)){t=e;this.returnType=this.returnType.substring(0,this.returnType.length-1)}}}}else{const s=e.length;const i=e.substring(0,1);const n=e.substring(s-1);if(c.includes(i)){this.visibility=i}if(/[$*]/.exec(n)){t=n}this.id=e.substring(this.visibility===""?0:1,t===""?s:s-1)}this.classifier=t;this.id=this.id.startsWith(" ")?" "+this.id.trim():this.id.trim();const s=`${this.visibility?"\\"+this.visibility:""}${(0,r.QO)(this.id)}${this.memberType==="method"?`(${(0,r.QO)(this.parameters)})${this.returnType?" : "+(0,r.QO)(this.returnType):""}`:""}`;this.text=s.replaceAll("<","&lt;").replaceAll(">","&gt;");if(this.text.startsWith("\\&lt;")){this.text=this.text.replace("\\&lt;","~")}}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}};var p="classId-";var d=0;var A=(0,r.K2)((e=>r.Y2.sanitizeText(e,(0,r.D7)())),"sanitizeText");var b=class{constructor(){this.relations=[];this.classes=new Map;this.styleClasses=new Map;this.notes=[];this.interfaces=[];this.namespaces=new Map;this.namespaceCounter=0;this.functions=[];this.lineType={LINE:0,DOTTED_LINE:1};this.relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4};this.setupToolTips=(0,r.K2)((e=>{let t=(0,u.Ltv)(".mermaidTooltip");if((t._groups||t)[0][0]===null){t=(0,u.Ltv)("body").append("div").attr("class","mermaidTooltip").style("opacity",0)}const s=(0,u.Ltv)(e).select("svg");const i=s.selectAll("g.node");i.on("mouseover",(e=>{const s=(0,u.Ltv)(e.currentTarget);const i=s.attr("title");if(i===null){return}const n=this.getBoundingClientRect();t.transition().duration(200).style("opacity",".9");t.text(s.attr("title")).style("left",window.scrollX+n.left+(n.right-n.left)/2+"px").style("top",window.scrollY+n.top-14+document.body.scrollTop+"px");t.html(t.html().replace(/&lt;br\/&gt;/g,"<br/>"));s.classed("hover",true)})).on("mouseout",(e=>{t.transition().duration(500).style("opacity",0);const s=(0,u.Ltv)(e.currentTarget);s.classed("hover",false)}))}),"setupToolTips");this.direction="TB";this.setAccTitle=r.SV;this.getAccTitle=r.iN;this.setAccDescription=r.EI;this.getAccDescription=r.m7;this.setDiagramTitle=r.ke;this.getDiagramTitle=r.ab;this.getConfig=(0,r.K2)((()=>(0,r.D7)().class),"getConfig");this.functions.push(this.setupToolTips.bind(this));this.clear();this.addRelation=this.addRelation.bind(this);this.addClassesToNamespace=this.addClassesToNamespace.bind(this);this.addNamespace=this.addNamespace.bind(this);this.setCssClass=this.setCssClass.bind(this);this.addMembers=this.addMembers.bind(this);this.addClass=this.addClass.bind(this);this.setClassLabel=this.setClassLabel.bind(this);this.addAnnotation=this.addAnnotation.bind(this);this.addMember=this.addMember.bind(this);this.cleanupLabel=this.cleanupLabel.bind(this);this.addNote=this.addNote.bind(this);this.defineClass=this.defineClass.bind(this);this.setDirection=this.setDirection.bind(this);this.setLink=this.setLink.bind(this);this.bindFunctions=this.bindFunctions.bind(this);this.clear=this.clear.bind(this);this.setTooltip=this.setTooltip.bind(this);this.setClickEvent=this.setClickEvent.bind(this);this.setCssStyle=this.setCssStyle.bind(this)}static{(0,r.K2)(this,"ClassDB")}splitClassNameAndType(e){const t=r.Y2.sanitizeText(e,(0,r.D7)());let s="";let i=t;if(t.indexOf("~")>0){const e=t.split("~");i=A(e[0]);s=A(e[1])}return{className:i,type:s}}setClassLabel(e,t){const s=r.Y2.sanitizeText(e,(0,r.D7)());if(t){t=A(t)}const{className:i}=this.splitClassNameAndType(s);this.classes.get(i).label=t;this.classes.get(i).text=`${t}${this.classes.get(i).type?`<${this.classes.get(i).type}>`:""}`}addClass(e){const t=r.Y2.sanitizeText(e,(0,r.D7)());const{className:s,type:i}=this.splitClassNameAndType(t);if(this.classes.has(s)){return}const n=r.Y2.sanitizeText(s,(0,r.D7)());this.classes.set(n,{id:n,type:i,label:n,text:`${n}${i?`&lt;${i}&gt;`:""}`,shape:"classBox",cssClasses:"default",methods:[],members:[],annotations:[],styles:[],domId:p+n+"-"+d});d++}addInterface(e,t){const s={id:`interface${this.interfaces.length}`,label:e,classId:t};this.interfaces.push(s)}lookUpDomId(e){const t=r.Y2.sanitizeText(e,(0,r.D7)());if(this.classes.has(t)){return this.classes.get(t).domId}throw new Error("Class not found: "+t)}clear(){this.relations=[];this.classes=new Map;this.notes=[];this.interfaces=[];this.functions=[];this.functions.push(this.setupToolTips.bind(this));this.namespaces=new Map;this.namespaceCounter=0;this.direction="TB";(0,r.IU)()}getClass(e){return this.classes.get(e)}getClasses(){return this.classes}getRelations(){return this.relations}getNotes(){return this.notes}addRelation(e){r.Rm.debug("Adding relation: "+JSON.stringify(e));const t=[this.relationType.LOLLIPOP,this.relationType.AGGREGATION,this.relationType.COMPOSITION,this.relationType.DEPENDENCY,this.relationType.EXTENSION];if(e.relation.type1===this.relationType.LOLLIPOP&&!t.includes(e.relation.type2)){this.addClass(e.id2);this.addInterface(e.id1,e.id2);e.id1=`interface${this.interfaces.length-1}`}else if(e.relation.type2===this.relationType.LOLLIPOP&&!t.includes(e.relation.type1)){this.addClass(e.id1);this.addInterface(e.id2,e.id1);e.id2=`interface${this.interfaces.length-1}`}else{this.addClass(e.id1);this.addClass(e.id2)}e.id1=this.splitClassNameAndType(e.id1).className;e.id2=this.splitClassNameAndType(e.id2).className;e.relationTitle1=r.Y2.sanitizeText(e.relationTitle1.trim(),(0,r.D7)());e.relationTitle2=r.Y2.sanitizeText(e.relationTitle2.trim(),(0,r.D7)());this.relations.push(e)}addAnnotation(e,t){const s=this.splitClassNameAndType(e).className;this.classes.get(s).annotations.push(t)}addMember(e,t){this.addClass(e);const s=this.splitClassNameAndType(e).className;const i=this.classes.get(s);if(typeof t==="string"){const e=t.trim();if(e.startsWith("<<")&&e.endsWith(">>")){i.annotations.push(A(e.substring(2,e.length-2)))}else if(e.indexOf(")")>0){i.methods.push(new h(e,"method"))}else if(e){i.members.push(new h(e,"attribute"))}}}addMembers(e,t){if(Array.isArray(t)){t.reverse();t.forEach((t=>this.addMember(e,t)))}}addNote(e,t){const s={id:`note${this.notes.length}`,class:t,text:e};this.notes.push(s)}cleanupLabel(e){if(e.startsWith(":")){e=e.substring(1)}return A(e.trim())}setCssClass(e,t){e.split(",").forEach((e=>{let s=e;if(/\d/.exec(e[0])){s=p+s}const i=this.classes.get(s);if(i){i.cssClasses+=" "+t}}))}defineClass(e,t){for(const s of e){let e=this.styleClasses.get(s);if(e===void 0){e={id:s,styles:[],textStyles:[]};this.styleClasses.set(s,e)}if(t){t.forEach((t=>{if(/color/.exec(t)){const s=t.replace("fill","bgFill");e.textStyles.push(s)}e.styles.push(t)}))}this.classes.forEach((e=>{if(e.cssClasses.includes(s)){e.styles.push(...t.flatMap((e=>e.split(","))))}}))}}setTooltip(e,t){e.split(",").forEach((e=>{if(t!==void 0){this.classes.get(e).tooltip=A(t)}}))}getTooltip(e,t){if(t&&this.namespaces.has(t)){return this.namespaces.get(t).classes.get(e).tooltip}return this.classes.get(e).tooltip}setLink(e,t,s){const i=(0,r.D7)();e.split(",").forEach((e=>{let n=e;if(/\d/.exec(e[0])){n=p+n}const r=this.classes.get(n);if(r){r.link=a._K.formatUrl(t,i);if(i.securityLevel==="sandbox"){r.linkTarget="_top"}else if(typeof s==="string"){r.linkTarget=A(s)}else{r.linkTarget="_blank"}}}));this.setCssClass(e,"clickable")}setClickEvent(e,t,s){e.split(",").forEach((e=>{this.setClickFunc(e,t,s);this.classes.get(e).haveCallback=true}));this.setCssClass(e,"clickable")}setClickFunc(e,t,s){const i=r.Y2.sanitizeText(e,(0,r.D7)());const n=(0,r.D7)();if(n.securityLevel!=="loose"){return}if(t===void 0){return}const u=i;if(this.classes.has(u)){const e=this.lookUpDomId(u);let i=[];if(typeof s==="string"){i=s.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let e=0;e<i.length;e++){let t=i[e].trim();if(t.startsWith('"')&&t.endsWith('"')){t=t.substr(1,t.length-2)}i[e]=t}}if(i.length===0){i.push(e)}this.functions.push((()=>{const s=document.querySelector(`[id="${e}"]`);if(s!==null){s.addEventListener("click",(()=>{a._K.runFunc(t,...i)}),false)}}))}}bindFunctions(e){this.functions.forEach((t=>{t(e)}))}getDirection(){return this.direction}setDirection(e){this.direction=e}addNamespace(e){if(this.namespaces.has(e)){return}this.namespaces.set(e,{id:e,classes:new Map,children:{},domId:p+e+"-"+this.namespaceCounter});this.namespaceCounter++}getNamespace(e){return this.namespaces.get(e)}getNamespaces(){return this.namespaces}addClassesToNamespace(e,t){if(!this.namespaces.has(e)){return}for(const s of t){const{className:t}=this.splitClassNameAndType(s);this.classes.get(t).parent=e;this.namespaces.get(e).classes.set(t,this.classes.get(t))}}setCssStyle(e,t){const s=this.classes.get(e);if(!t||!s){return}for(const i of t){if(i.includes(",")){s.styles.push(...i.split(","))}else{s.styles.push(i)}}}getArrowMarker(e){let t;switch(e){case 0:t="aggregation";break;case 1:t="extension";break;case 2:t="composition";break;case 3:t="dependency";break;case 4:t="lollipop";break;default:t="none"}return t}getData(){const e=[];const t=[];const s=(0,r.D7)();for(const n of this.namespaces.keys()){const t=this.namespaces.get(n);if(t){const i={id:t.id,label:t.id,isGroup:true,padding:s.class.padding??16,shape:"rect",cssStyles:["fill: none","stroke: black"],look:s.look};e.push(i)}}for(const n of this.classes.keys()){const t=this.classes.get(n);if(t){const i=t;i.parentId=t.parent;i.look=s.look;e.push(i)}}let i=0;for(const n of this.notes){i++;const a={id:n.id,label:n.text,isGroup:false,shape:"note",padding:s.class.padding??6,cssStyles:["text-align: left","white-space: nowrap",`fill: ${s.themeVariables.noteBkgColor}`,`stroke: ${s.themeVariables.noteBorderColor}`],look:s.look};e.push(a);const r=this.classes.get(n.class)?.id??"";if(r){const e={id:`edgeNote${i}`,start:n.id,end:r,type:"normal",thickness:"normal",classes:"relation",arrowTypeStart:"none",arrowTypeEnd:"none",arrowheadStyle:"",labelStyle:[""],style:["fill: none"],pattern:"dotted",look:s.look};t.push(e)}}for(const n of this.interfaces){const t={id:n.id,label:n.label,isGroup:false,shape:"rect",cssStyles:["opacity: 0;"],look:s.look};e.push(t)}i=0;for(const n of this.relations){i++;const e={id:(0,a.rY)(n.id1,n.id2,{prefix:"id",counter:i}),start:n.id1,end:n.id2,type:"normal",label:n.title,labelpos:"c",thickness:"normal",classes:"relation",arrowTypeStart:this.getArrowMarker(n.relation.type1),arrowTypeEnd:this.getArrowMarker(n.relation.type2),startLabelRight:n.relationTitle1==="none"?"":n.relationTitle1,endLabelLeft:n.relationTitle2==="none"?"":n.relationTitle2,arrowheadStyle:"",labelStyle:["display: inline-block"],style:n.style||"",pattern:n.relation.lineType==1?"dashed":"solid",look:s.look};t.push(e)}return{nodes:e,edges:t,other:{},config:s,direction:this.getDirection()}}};var y=(0,r.K2)((e=>`g.classGroup text {\n  fill: ${e.nodeBorder||e.classText};\n  stroke: none;\n  font-family: ${e.fontFamily};\n  font-size: 10px;\n\n  .title {\n    font-weight: bolder;\n  }\n\n}\n\n.nodeLabel, .edgeLabel {\n  color: ${e.classText};\n}\n.edgeLabel .label rect {\n  fill: ${e.mainBkg};\n}\n.label text {\n  fill: ${e.classText};\n}\n\n.labelBkg {\n  background: ${e.mainBkg};\n}\n.edgeLabel .label span {\n  background: ${e.mainBkg};\n}\n\n.classTitle {\n  font-weight: bolder;\n}\n.node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${e.mainBkg};\n    stroke: ${e.nodeBorder};\n    stroke-width: 1px;\n  }\n\n\n.divider {\n  stroke: ${e.nodeBorder};\n  stroke-width: 1;\n}\n\ng.clickable {\n  cursor: pointer;\n}\n\ng.classGroup rect {\n  fill: ${e.mainBkg};\n  stroke: ${e.nodeBorder};\n}\n\ng.classGroup line {\n  stroke: ${e.nodeBorder};\n  stroke-width: 1;\n}\n\n.classLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${e.mainBkg};\n  opacity: 0.5;\n}\n\n.classLabel .label {\n  fill: ${e.nodeBorder};\n  font-size: 10px;\n}\n\n.relation {\n  stroke: ${e.lineColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.dashed-line{\n  stroke-dasharray: 3;\n}\n\n.dotted-line{\n  stroke-dasharray: 1 2;\n}\n\n#compositionStart, .composition {\n  fill: ${e.lineColor} !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#compositionEnd, .composition {\n  fill: ${e.lineColor} !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#dependencyStart, .dependency {\n  fill: ${e.lineColor} !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#dependencyStart, .dependency {\n  fill: ${e.lineColor} !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#extensionStart, .extension {\n  fill: transparent !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#extensionEnd, .extension {\n  fill: transparent !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#aggregationStart, .aggregation {\n  fill: transparent !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#aggregationEnd, .aggregation {\n  fill: transparent !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#lollipopStart, .lollipop {\n  fill: ${e.mainBkg} !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n#lollipopEnd, .lollipop {\n  fill: ${e.mainBkg} !important;\n  stroke: ${e.lineColor} !important;\n  stroke-width: 1;\n}\n\n.edgeTerminals {\n  font-size: 11px;\n  line-height: initial;\n}\n\n.classTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${e.textColor};\n}\n`),"getStyles");var f=y;var k=(0,r.K2)(((e,t="TB")=>{if(!e.doc){return t}let s=t;for(const i of e.doc){if(i.stmt==="dir"){s=i.value}}return s}),"getDir");var C=(0,r.K2)((function(e,t){return t.db.getClasses()}),"getClasses");var g=(0,r.K2)((async function(e,t,s,u){r.Rm.info("REF0:");r.Rm.info("Drawing class diagram (v3)",t);const{securityLevel:l,state:o,layout:c}=(0,r.D7)();const h=u.db.getData();const p=(0,i.A)(t,l);h.type=u.type;h.layoutAlgorithm=(0,n.q7)(c);h.nodeSpacing=o?.nodeSpacing||50;h.rankSpacing=o?.rankSpacing||50;h.markers=["aggregation","extension","composition","dependency","lollipop"];h.diagramId=t;await(0,n.XX)(h,p);const d=8;a._K.insertTitle(p,"classDiagramTitleText",o?.titleTopMargin??25,u.db.getDiagramTitle());(0,i.P)(p,d,"classDiagram",o?.useMaxWidth??true)}),"draw");var m={getClasses:C,draw:g,getDir:k}},15051:(e,t,s)=>{s.d(t,{A:()=>a,P:()=>r});var i=s(75905);var n=s(24982);var a=(0,i.K2)(((e,t)=>{let s;if(t==="sandbox"){s=(0,n.Ltv)("#i"+e)}const i=t==="sandbox"?(0,n.Ltv)(s.nodes()[0].contentDocument.body):(0,n.Ltv)("body");const a=i.select(`[id="${e}"]`);return a}),"getDiagramElement");var r=(0,i.K2)(((e,t,s,n)=>{e.attr("class",s);const{width:a,height:r,x:o,y:c}=u(e,t);(0,i.a$)(e,r,a,n);const h=l(o,c,a,r,t);e.attr("viewBox",h);i.Rm.debug(`viewBox configured: ${h} with padding: ${t}`)}),"setupViewPortForSVG");var u=(0,i.K2)(((e,t)=>{const s=e.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+t*2,height:s.height+t*2,x:s.x,y:s.y}}),"calculateDimensionsWithPadding");var l=(0,i.K2)(((e,t,s,i,n)=>`${e-n} ${t-n} ${s} ${i}`),"createViewBox")}}]);