#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证1000指数每日上涨概率统计分析 - Streamlit应用
适合老人家使用的简洁界面
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# 页面配置
st.set_page_config(
    page_title="中证1000指数分析",
    page_icon="📈",
    layout="wide"
)

@st.cache_data(ttl=3600)  # 缓存1小时
def get_csi1000_data():
    """
    获取中证1000指数数据
    """
    try:
        import akshare as ak
        
        with st.spinner('正在获取中证1000指数数据...'):
            # 使用最稳定的东方财富接口
            df = ak.stock_zh_index_daily_em(
                symbol="sz000852",
                start_date="20230101",
                end_date="20241231"
            )
            
            if df is not None and not df.empty:
                # 重命名列
                column_mapping = {
                    'date': '日期',
                    'close': '收盘',
                    'open': '开盘',
                    'high': '最高',
                    'low': '最低',
                    'volume': '成交量',
                    'amount': '成交额'
                }
                df = df.rename(columns=column_mapping)
                
                return df
            else:
                st.error("获取数据失败，请稍后重试")
                return None
                
    except Exception as e:
        st.error(f"数据获取错误: {e}")
        return None

def calculate_daily_returns(df):
    """
    计算每日收益率和涨跌情况
    """
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    df = df.sort_values('日期').reset_index(drop=True)
    
    # 计算日收益率
    df['收益率'] = df['收盘'].pct_change()
    df['是否上涨'] = df['收益率'] > 0
    
    # 添加星期信息
    df['星期几'] = df['日期'].dt.dayofweek
    df['星期几_中文'] = df['星期几'].map({
        0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五'
    })
    
    # 只保留工作日
    df = df[df['星期几'] < 5].copy()
    df = df.dropna(subset=['收益率']).reset_index(drop=True)
    
    return df

def analyze_weekly_probability(df):
    """
    分析每个工作日的上涨概率
    """
    weekly_stats = df.groupby('星期几_中文').agg({
        '是否上涨': ['count', 'sum', 'mean'],
        '收益率': ['mean', 'std']
    }).round(4)
    
    weekly_stats.columns = ['总交易日数', '上涨天数', '上涨概率', '平均收益率', '收益率标准差']
    
    # 按星期顺序排序
    weekday_order = ['星期一', '星期二', '星期三', '星期四', '星期五']
    weekly_stats = weekly_stats.reindex(weekday_order)
    
    return weekly_stats

def create_probability_chart(weekly_stats):
    """
    创建上涨概率图表 - 简化版
    """
    fig = go.Figure()
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    fig.add_trace(go.Bar(
        x=weekly_stats.index,
        y=weekly_stats['上涨概率'],
        marker_color=colors,
        text=[f'{prob:.1%}' for prob in weekly_stats['上涨概率']],
        textposition='outside',
        textfont=dict(size=18, color='black'),
        name='上涨概率'
    ))
    
    fig.update_layout(
        title='中证1000指数 - 各工作日上涨概率',
        xaxis_title='工作日',
        yaxis_title='上涨概率',
        yaxis_tickformat='.0%',
        yaxis_range=[0, 1],
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        showlegend=False,
        font=dict(size=14)
    )
    
    return fig

def create_return_chart(weekly_stats):
    """
    创建平均收益率图表
    """
    fig = go.Figure()
    
    colors = ['green' if x >= 0 else 'red' for x in weekly_stats['平均收益率']]
    
    fig.add_trace(go.Bar(
        x=weekly_stats.index,
        y=weekly_stats['平均收益率'],
        marker_color=colors,
        text=[f'{ret:.2%}' for ret in weekly_stats['平均收益率']],
        textposition='outside',
        textfont=dict(size=18, color='black'),
        name='平均收益率'
    ))
    
    fig.update_layout(
        title='中证1000指数 - 各工作日平均收益率',
        xaxis_title='工作日',
        yaxis_title='平均收益率',
        yaxis_tickformat='.2%',
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        showlegend=False,
        font=dict(size=14)
    )
    
    # 添加零线
    fig.add_hline(y=0, line_dash="dash", line_color="gray", line_width=2)
    
    return fig

def create_summary_table(weekly_stats):
    """
    创建汇总表格
    """
    # 准备表格数据
    table_data = []
    for day in weekly_stats.index:
        if pd.notna(weekly_stats.loc[day, '总交易日数']):
            stats = weekly_stats.loc[day]
            table_data.append({
                '工作日': day,
                '交易日数': int(stats['总交易日数']),
                '上涨天数': int(stats['上涨天数']),
                '上涨概率': f"{stats['上涨概率']:.1%}",
                '平均收益率': f"{stats['平均收益率']:.2%}",
                '收益率标准差': f"{stats['收益率标准差']:.2%}"
            })
    
    return pd.DataFrame(table_data)

def main():
    """
    主应用
    """
    # 标题
    st.title("📈 中证1000指数每日上涨概率分析")
    st.markdown("---")
    
    # 获取数据
    df = get_csi1000_data()
    
    if df is None:
        st.error("无法获取数据，请检查网络连接后刷新页面")
        return
    
    # 处理数据
    df = calculate_daily_returns(df)
    weekly_stats = analyze_weekly_probability(df)
    
    # 显示基本信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📅 分析时间段",
            value=f"{df['日期'].min().strftime('%Y-%m-%d')} 至 {df['日期'].max().strftime('%Y-%m-%d')}"
        )
    
    with col2:
        st.metric(
            label="📊 总交易日数",
            value=f"{len(df)} 天"
        )
    
    with col3:
        st.metric(
            label="📈 总上涨天数",
            value=f"{df['是否上涨'].sum()} 天"
        )
    
    with col4:
        st.metric(
            label="🎯 整体上涨概率",
            value=f"{df['是否上涨'].mean():.1%}"
        )
    
    st.markdown("---")
    
    # 图表展示
    col1, col2 = st.columns(2)
    
    with col1:
        st.plotly_chart(create_probability_chart(weekly_stats), use_container_width=True)
    
    with col2:
        st.plotly_chart(create_return_chart(weekly_stats), use_container_width=True)
    
    # 详细数据表格
    st.markdown("### 📋 详细统计数据")
    summary_table = create_summary_table(weekly_stats)
    st.dataframe(summary_table, use_container_width=True, hide_index=True)
    
    # 主要发现
    st.markdown("### 🔍 主要发现")
    
    valid_stats = weekly_stats.dropna()
    if not valid_stats.empty:
        max_prob_day = valid_stats['上涨概率'].idxmax()
        min_prob_day = valid_stats['上涨概率'].idxmin()
        max_return_day = valid_stats['平均收益率'].idxmax()
        min_return_day = valid_stats['平均收益率'].idxmin()
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.success(f"🏆 上涨概率最高: {max_prob_day} ({valid_stats.loc[max_prob_day, '上涨概率']:.1%})")
            st.success(f"💰 平均收益率最高: {max_return_day} ({valid_stats.loc[max_return_day, '平均收益率']:.2%})")
        
        with col2:
            st.error(f"📉 上涨概率最低: {min_prob_day} ({valid_stats.loc[min_prob_day, '上涨概率']:.1%})")
            st.error(f"💸 平均收益率最低: {min_return_day} ({valid_stats.loc[min_return_day, '平均收益率']:.2%})")
        
        # 概率差异
        prob_range = valid_stats['上涨概率'].max() - valid_stats['上涨概率'].min()
        st.info(f"📊 各工作日上涨概率差异: {prob_range:.1%}")
        
        if prob_range > 0.1:
            st.warning("⚠️ 不同工作日的上涨概率存在较明显差异，投资时可考虑这一因素")
        else:
            st.success("✅ 不同工作日的上涨概率相对均衡")
    
    # 数据下载
    st.markdown("### 💾 数据下载")
    col1, col2 = st.columns(2)
    
    with col1:
        csv_data = summary_table.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="📊 下载统计数据 (CSV)",
            data=csv_data,
            file_name=f"中证1000指数统计_{datetime.now().strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )
    
    with col2:
        raw_csv = df.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="📈 下载原始数据 (CSV)",
            data=raw_csv,
            file_name=f"中证1000指数原始数据_{datetime.now().strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )

if __name__ == "__main__":
    main()
