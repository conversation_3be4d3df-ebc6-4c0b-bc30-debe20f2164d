#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版中证1000指数每日上涨概率统计
专门用于获取真实数据的版本
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

def get_real_data():
    """
    尝试获取真实的中证1000指数数据
    """
    try:
        print("正在获取中证1000指数真实数据...")
        
        # 设置时间范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        
        print(f"查询时间范围: {start_date} 到 {end_date}")
        
        # 尝试不同的获取方式
        methods = [
            lambda: ak.index_zh_a_hist(symbol="000852", period="daily", start_date=start_date, end_date=end_date),
            lambda: ak.stock_zh_index_daily(symbol="sz000852"),
            lambda: ak.index_zh_a_hist_min_em(symbol="000852", start_date=start_date, end_date=end_date, period="1日"),
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                print(f"尝试方法 {i}...")
                df = method()
                if df is not None and not df.empty:
                    print(f"成功获取数据！共{len(df)}条记录")
                    return df
            except Exception as e:
                print(f"方法 {i} 失败: {e}")
                continue
        
        print("所有方法都失败了")
        return None
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return None

def simple_analysis(df):
    """
    简单的每日上涨概率分析
    """
    if df is None or df.empty:
        print("没有数据可供分析")
        return
    
    print(f"\n数据概况:")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print(f"前5行数据:")
    print(df.head())
    
    # 尝试找到收盘价列
    close_col = None
    for col in ['收盘', 'close', 'Close', '收盘价']:
        if col in df.columns:
            close_col = col
            break
    
    if close_col is None:
        print("未找到收盘价列，无法进行分析")
        return
    
    # 计算收益率
    df['收益率'] = df[close_col].pct_change()
    df['是否上涨'] = df['收益率'] > 0
    
    # 添加星期信息
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])
    elif 'date' in df.columns:
        df['日期'] = pd.to_datetime(df['date'])
        
    if '日期' in df.columns:
        df['星期几'] = df['日期'].dt.dayofweek
        df['星期几_中文'] = df['星期几'].map({
            0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五'
        })
        
        # 只保留工作日
        df = df[df['星期几'] < 5].dropna(subset=['收益率'])
        
        if len(df) > 0:
            # 计算每日上涨概率
            weekly_stats = df.groupby('星期几_中文').agg({
                '是否上涨': ['count', 'sum', 'mean'],
                '收益率': ['mean', 'std']
            }).round(4)
            
            weekly_stats.columns = ['总交易日数', '上涨天数', '上涨概率', '平均收益率', '收益率标准差']
            
            print(f"\n中证1000指数每日上涨概率统计:")
            print("="*60)
            print(weekly_stats)
            
            # 保存结果
            weekly_stats.to_csv('真实数据_中证1000指数每日上涨概率统计.csv', encoding='utf-8-sig')
            df.to_csv('真实数据_中证1000指数历史数据.csv', encoding='utf-8-sig', index=False)
            
            print(f"\n结果已保存到:")
            print("  - 真实数据_中证1000指数每日上涨概率统计.csv")
            print("  - 真实数据_中证1000指数历史数据.csv")
        else:
            print("过滤后没有有效数据")
    else:
        print("未找到日期列，无法按星期分析")

def main():
    """
    主函数
    """
    print("简化版中证1000指数每日上涨概率统计")
    print("="*50)
    
    # 获取真实数据
    df = get_real_data()
    
    # 分析数据
    simple_analysis(df)

if __name__ == "__main__":
    main()
