import{s as m,r as C,aK as E,aE as h,j as a,u as L,aJ as S,l as y}from"./index.DKN5MVff.js";import{w as W,E as b}from"./withFullScreenWrapper.C-gXt0Rl.js";import{S as u,T as v}from"./Toolbar.Dt4jIKlY.js";const T=m("div",{target:"evl31sl0"})(({theme:e})=>({display:"flex",flexDirection:"row",flexWrap:"wrap",rowGap:e.spacing.lg,maxWidth:"100%",width:"fit-content"})),F=m("div",{target:"evl31sl1"})(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"stretch",width:"auto",flexGrow:0,">img":{borderRadius:e.radii.default}})),j=m("div",{target:"evl31sl2"})(({theme:e})=>({textAlign:"center",marginTop:e.spacing.xs,wordWrap:"break-word",padding:e.spacing.threeXS})),M=y.getLogger("ImageList"),R=({itemKey:e,image:t,imgStyle:n,buildMediaURL:i,handleImageError:c})=>{const s=L(t.url);return h(F,{"data-testid":"stImageContainer",children:[a("img",{style:n,src:i(t.url),alt:e,onError:c,crossOrigin:s}),t.caption&&a(j,{"data-testid":"stImageCaption",style:n,children:a(S,{source:t.caption,allowHTML:!1,isCaption:!0,isLabel:!0})})]})};function G({element:e,endpoints:t,disableFullscreenMode:n}){const{expanded:i,width:c,height:s,expand:x,collapse:f}=E(b),p=c||0;let d;const o=e.width;if([-1,-3,-4].includes(o))d=void 0;else if([-2,-5].includes(o))d=p;else if(o>0)d=o;else throw Error(`Invalid image width: ${o}`);const r={};s&&i?(r.maxHeight=s,r.objectFit="contain",r.width="100%"):(r.width=d??"100%",r.maxWidth="100%");const w=g=>{const l=g.currentTarget.src;M.error(`Client Error: Image source error - ${l}`),t.sendClientErrorToHost("Image","Image source failed to load","onerror triggered",l)};return h(u,{width:p,height:s,useContainerWidth:i,topCentered:!0,children:[a(v,{target:u,isFullScreen:i,onExpand:x,onCollapse:f,disableFullscreenMode:n}),a(T,{className:"stImage","data-testid":"stImage",children:e.imgs.map((g,l)=>a(R,{itemKey:l.toString(),image:g,imgStyle:r,buildMediaURL:I=>t.buildMediaURL(I),handleImageError:w},l))})]})}const H=W(G),k=C.memo(H);export{k as default};
