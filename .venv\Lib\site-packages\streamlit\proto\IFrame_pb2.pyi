"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class IFrame(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    SRCDOC_FIELD_NUMBER: builtins.int
    WIDTH_FIELD_NUMBER: builtins.int
    HAS_WIDTH_FIELD_NUMBER: builtins.int
    HEIGHT_FIELD_NUMBER: builtins.int
    SCROLLING_FIELD_NUMBER: builtins.int
    TAB_INDEX_FIELD_NUMBER: builtins.int
    src: builtins.str
    """A URL to load"""
    srcdoc: builtins.str
    """Inline HTML"""
    width: builtins.float
    """Deprecated: Use width_config on Element.proto instead"""
    has_width: builtins.bool
    """Deprecated: Width is now handled through width_config on Element.proto"""
    height: builtins.float
    """Deprecated: Use height_config on Element.proto instead"""
    scrolling: builtins.bool
    tab_index: builtins.int
    def __init__(
        self,
        *,
        src: builtins.str = ...,
        srcdoc: builtins.str = ...,
        width: builtins.float = ...,
        has_width: builtins.bool = ...,
        height: builtins.float = ...,
        scrolling: builtins.bool = ...,
        tab_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_tab_index", b"_tab_index", "src", b"src", "srcdoc", b"srcdoc", "tab_index", b"tab_index", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_tab_index", b"_tab_index", "has_width", b"has_width", "height", b"height", "scrolling", b"scrolling", "src", b"src", "srcdoc", b"srcdoc", "tab_index", b"tab_index", "type", b"type", "width", b"width"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_tab_index", b"_tab_index"]) -> typing.Literal["tab_index"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["type", b"type"]) -> typing.Literal["src", "srcdoc"] | None: ...

global___IFrame = IFrame
