#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复akshare连接问题的中证1000指数分析程序
基于akshare官方文档的最佳实践
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import time

warnings.filterwarnings('ignore')

def setup_akshare():
    """
    设置akshare环境
    """
    try:
        import akshare as ak
        print("akshare版本:", ak.__version__)
        return True
    except ImportError:
        print("请先安装akshare: uv add akshare")
        return False

def get_csi1000_data_v1():
    """
    方法1: 使用index_zh_a_hist通用接口（推荐）
    """
    import akshare as ak
    
    print("尝试方法1: index_zh_a_hist通用接口...")
    
    try:
        # 根据文档，这是最稳定的接口
        df = ak.index_zh_a_hist(
            symbol="000852",  # 中证1000指数代码（不需要市场前缀）
            period="daily",
            start_date="20230101",  # 开始日期
            end_date="22220101"     # 结束日期设为未来
        )
        
        if df is not None and not df.empty:
            print(f"✓ 成功获取{len(df)}条数据")
            return df
        else:
            raise ValueError("返回数据为空")
            
    except Exception as e:
        print(f"✗ 方法1失败: {e}")
        return None

def get_csi1000_data_v2():
    """
    方法2: 使用stock_zh_index_daily_em东方财富接口
    """
    import akshare as ak
    
    print("尝试方法2: stock_zh_index_daily_em东方财富接口...")
    
    try:
        # 使用东方财富的接口，支持sz/sh/csi前缀
        df = ak.stock_zh_index_daily_em(
            symbol="sz000852",  # 深交所中证1000
            start_date="20230101",
            end_date="20241231"
        )
        
        if df is not None and not df.empty:
            # 重命名列以保持一致性
            if 'date' in df.columns:
                df = df.rename(columns={'date': '日期'})
            if 'close' in df.columns:
                df = df.rename(columns={'close': '收盘'})
            
            print(f"✓ 成功获取{len(df)}条数据")
            return df
        else:
            raise ValueError("返回数据为空")
            
    except Exception as e:
        print(f"✗ 方法2失败: {e}")
        return None

def get_csi1000_data_v3():
    """
    方法3: 使用stock_zh_index_daily新浪接口
    """
    import akshare as ak
    
    print("尝试方法3: stock_zh_index_daily新浪接口...")
    
    try:
        # 新浪财经接口
        df = ak.stock_zh_index_daily(symbol="sz000852")
        
        if df is not None and not df.empty:
            print(f"✓ 成功获取{len(df)}条数据")
            return df
        else:
            raise ValueError("返回数据为空")
            
    except Exception as e:
        print(f"✗ 方法3失败: {e}")
        return None

def get_csi1000_data_v4():
    """
    方法4: 使用中证指数官方接口
    """
    import akshare as ak
    
    print("尝试方法4: stock_zh_index_hist_csindex中证指数官方接口...")
    
    try:
        # 中证指数官方数据
        df = ak.stock_zh_index_hist_csindex(
            symbol="000852",
            start_date="20230101",
            end_date="20241231"
        )
        
        if df is not None and not df.empty:
            print(f"✓ 成功获取{len(df)}条数据")
            return df
        else:
            raise ValueError("返回数据为空")
            
    except Exception as e:
        print(f"✗ 方法4失败: {e}")
        return None

def try_all_methods():
    """
    尝试所有方法获取数据
    """
    print("开始尝试获取中证1000指数数据...")
    print("=" * 50)
    
    methods = [
        ("通用接口", get_csi1000_data_v1),
        ("东方财富", get_csi1000_data_v2),
        ("新浪财经", get_csi1000_data_v3),
        ("中证指数", get_csi1000_data_v4),
    ]
    
    for method_name, method_func in methods:
        try:
            df = method_func()
            if df is not None and not df.empty:
                print(f"\n🎉 {method_name}成功！")
                return df, method_name
        except Exception as e:
            print(f"❌ {method_name}异常: {e}")
        
        print("等待2秒后尝试下一个方法...")
        time.sleep(2)
    
    print("\n❌ 所有方法都失败了")
    return None, None

def analyze_data(df, method_name):
    """
    分析获取到的数据
    """
    if df is None or df.empty:
        print("没有数据可供分析")
        return
    
    print(f"\n数据分析 (数据来源: {method_name})")
    print("=" * 50)
    
    # 显示数据基本信息
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 显示前几行数据
    print(f"\n前5行数据:")
    print(df.head())
    
    # 检查日期列
    date_cols = [col for col in df.columns if '日期' in col or 'date' in col.lower()]
    if date_cols:
        date_col = date_cols[0]
        df[date_col] = pd.to_datetime(df[date_col])
        print(f"\n时间范围: {df[date_col].min()} 到 {df[date_col].max()}")
    
    # 检查收盘价列
    close_cols = [col for col in df.columns if '收盘' in col or 'close' in col.lower()]
    if close_cols:
        close_col = close_cols[0]
        print(f"\n收盘价统计:")
        print(f"最高: {df[close_col].max():.2f}")
        print(f"最低: {df[close_col].min():.2f}")
        print(f"平均: {df[close_col].mean():.2f}")
        
        # 计算简单的上涨概率
        if len(df) > 1:
            df['收益率'] = df[close_col].pct_change()
            df['是否上涨'] = df['收益率'] > 0
            
            # 添加星期信息
            if date_cols:
                df['星期几'] = df[date_col].dt.dayofweek
                df['星期几_中文'] = df['星期几'].map({
                    0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五'
                })
                
                # 只保留工作日
                workday_df = df[df['星期几'] < 5].dropna(subset=['收益率'])
                
                if len(workday_df) > 0:
                    # 计算每日上涨概率
                    weekly_stats = workday_df.groupby('星期几_中文').agg({
                        '是否上涨': ['count', 'sum', 'mean'],
                        '收益率': ['mean', 'std']
                    }).round(4)
                    
                    weekly_stats.columns = ['总交易日数', '上涨天数', '上涨概率', '平均收益率', '收益率标准差']
                    
                    print(f"\n📊 中证1000指数每日上涨概率统计:")
                    print("-" * 60)
                    for day in ['星期一', '星期二', '星期三', '星期四', '星期五']:
                        if day in weekly_stats.index:
                            stats = weekly_stats.loc[day]
                            print(f"{day}: 上涨概率 {stats['上涨概率']:.2%}, "
                                  f"平均收益率 {stats['平均收益率']:.4%}, "
                                  f"交易日数 {int(stats['总交易日数'])}")
                    
                    # 保存结果
                    weekly_stats.to_csv(f'修复版_{method_name}_中证1000指数每日上涨概率统计.csv', 
                                        encoding='utf-8-sig')
                    df.to_csv(f'修复版_{method_name}_中证1000指数历史数据.csv', 
                              encoding='utf-8-sig', index=False)
                    
                    print(f"\n💾 结果已保存到:")
                    print(f"  - 修复版_{method_name}_中证1000指数每日上涨概率统计.csv")
                    print(f"  - 修复版_{method_name}_中证1000指数历史数据.csv")
                else:
                    print("过滤后没有有效的工作日数据")
            else:
                print("未找到日期列，无法按星期分析")
    else:
        print("未找到收盘价列")

def main():
    """
    主函数
    """
    print("akshare连接问题修复版 - 中证1000指数分析")
    print("=" * 60)
    
    # 检查akshare
    if not setup_akshare():
        return
    
    # 尝试获取数据
    df, method_name = try_all_methods()
    
    # 分析数据
    if df is not None:
        analyze_data(df, method_name)
    else:
        print("\n😞 无法获取数据，请检查网络连接或稍后重试")
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 更新akshare版本: uv add akshare>=1.17.34")
        print("3. 稍后重试（可能是数据源临时维护）")

if __name__ == "__main__":
    main()
