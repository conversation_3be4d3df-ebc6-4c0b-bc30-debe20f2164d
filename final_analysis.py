#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本：中证1000指数每日上涨概率统计分析
解决了akshare连接问题，包含完整的分析和可视化功能
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import time

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def get_csi1000_data():
    """
    获取中证1000指数数据 - 使用最可靠的方法
    """
    import akshare as ak
    
    print("正在获取中证1000指数数据...")
    
    try:
        # 使用东方财富接口，这是最稳定的
        df = ak.stock_zh_index_daily_em(
            symbol="sz000852",  # 深交所中证1000
            start_date="20230101",
            end_date="20241231"
        )
        
        if df is not None and not df.empty:
            # 重命名列以保持一致性
            if 'date' in df.columns:
                df = df.rename(columns={'date': '日期'})
            if 'close' in df.columns:
                df = df.rename(columns={'close': '收盘'})
            if 'open' in df.columns:
                df = df.rename(columns={'open': '开盘'})
            if 'high' in df.columns:
                df = df.rename(columns={'high': '最高'})
            if 'low' in df.columns:
                df = df.rename(columns={'low': '最低'})
            if 'volume' in df.columns:
                df = df.rename(columns={'volume': '成交量'})
            if 'amount' in df.columns:
                df = df.rename(columns={'amount': '成交额'})
            
            print(f"✓ 成功获取{len(df)}条数据")
            print(f"数据时间范围: {df['日期'].min()} 到 {df['日期'].max()}")
            return df
        else:
            raise ValueError("返回数据为空")
            
    except Exception as e:
        print(f"✗ 获取数据失败: {e}")
        return None

def calculate_daily_returns(df):
    """
    计算每日收益率和涨跌情况
    """
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    
    # 按日期排序
    df = df.sort_values('日期').reset_index(drop=True)
    
    # 计算日收益率
    df['收益率'] = df['收盘'].pct_change()
    
    # 判断是否上涨 (收益率 > 0)
    df['是否上涨'] = df['收益率'] > 0
    
    # 添加星期几信息
    df['星期几'] = df['日期'].dt.dayofweek
    df['星期几_中文'] = df['星期几'].map({
        0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 
        4: '星期五', 5: '星期六', 6: '星期日'
    })
    
    # 只保留工作日数据 (周一到周五)
    df = df[df['星期几'] < 5].copy()
    
    # 删除第一行（因为没有前一日数据计算收益率）
    df = df.dropna(subset=['收益率']).reset_index(drop=True)
    
    return df

def analyze_weekly_probability(df):
    """
    分析每个工作日的上涨概率
    """
    # 按星期几分组统计
    weekly_stats = df.groupby('星期几_中文').agg({
        '是否上涨': ['count', 'sum', 'mean'],
        '收益率': ['mean', 'std']
    }).round(4)
    
    # 重命名列
    weekly_stats.columns = ['总交易日数', '上涨天数', '上涨概率', '平均收益率', '收益率标准差']
    
    # 按星期顺序排序
    weekday_order = ['星期一', '星期二', '星期三', '星期四', '星期五']
    weekly_stats = weekly_stats.reindex(weekday_order)
    
    return weekly_stats

def create_visualizations(df, weekly_stats):
    """
    创建可视化图表
    """
    # 设置图表样式
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('中证1000指数每日上涨概率分析', fontsize=16, fontweight='bold')
    
    # 1. 上涨概率柱状图
    ax1 = axes[0, 0]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    bars = ax1.bar(weekly_stats.index, weekly_stats['上涨概率'], color=colors)
    ax1.set_title('各工作日上涨概率', fontweight='bold')
    ax1.set_ylabel('上涨概率')
    ax1.set_ylim(0, 1)
    
    # 在柱子上添加数值标签
    for bar, prob in zip(bars, weekly_stats['上涨概率']):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{prob:.1%}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 平均收益率柱状图
    ax2 = axes[0, 1]
    colors2 = ['red' if x < 0 else 'green' for x in weekly_stats['平均收益率']]
    bars2 = ax2.bar(weekly_stats.index, weekly_stats['平均收益率'], color=colors2, alpha=0.7)
    ax2.set_title('各工作日平均收益率', fontweight='bold')
    ax2.set_ylabel('平均收益率')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 添加数值标签
    for bar, ret in zip(bars2, weekly_stats['平均收益率']):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., 
                height + (0.0001 if height >= 0 else -0.0002),
                f'{ret:.2%}', ha='center', 
                va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 3. 交易日数统计
    ax3 = axes[1, 0]
    ax3.bar(weekly_stats.index, weekly_stats['总交易日数'], 
            color='lightblue', edgecolor='navy', alpha=0.7)
    ax3.set_title('各工作日交易日数统计', fontweight='bold')
    ax3.set_ylabel('交易日数')
    
    # 添加数值标签
    for i, (day, count) in enumerate(zip(weekly_stats.index, weekly_stats['总交易日数'])):
        ax3.text(i, count + 0.5, str(int(count)), ha='center', va='bottom', fontweight='bold')
    
    # 4. 收益率分布箱线图
    ax4 = axes[1, 1]
    weekday_order = ['星期一', '星期二', '星期三', '星期四', '星期五']
    data_for_box = [df[df['星期几_中文'] == day]['收益率'].values for day in weekday_order]
    
    box_plot = ax4.boxplot(data_for_box, labels=weekday_order, patch_artist=True)
    ax4.set_title('各工作日收益率分布', fontweight='bold')
    ax4.set_ylabel('收益率')
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 设置箱线图颜色
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    plt.tight_layout()
    plt.savefig('最终版_中证1000指数每日上涨概率分析.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 图表已保存为: 最终版_中证1000指数每日上涨概率分析.png")

def print_detailed_analysis(df, weekly_stats):
    """
    打印详细的分析结果
    """
    print("\n" + "="*60)
    print("中证1000指数每日上涨概率统计分析报告")
    print("="*60)
    
    print("\n数据概况:")
    print(f"  分析时间段: {df['日期'].min().strftime('%Y-%m-%d')} 至 {df['日期'].max().strftime('%Y-%m-%d')}")
    print(f"  总交易日数: {len(df)} 天")
    print(f"  总上涨天数: {df['是否上涨'].sum()} 天")
    print(f"  整体上涨概率: {df['是否上涨'].mean():.2%}")
    print(f"  平均日收益率: {df['收益率'].mean():.4%}")
    print(f"  收益率标准差: {df['收益率'].std():.4%}")
    
    print("\n各工作日详细统计:")
    print("-" * 80)
    print(f"{'星期':<8} {'交易日数':<10} {'上涨天数':<10} {'上涨概率':<12} {'平均收益率':<12} {'收益率标准差':<12}")
    print("-" * 80)
    
    for day in weekly_stats.index:
        if pd.notna(weekly_stats.loc[day, '总交易日数']):
            stats = weekly_stats.loc[day]
            print(f"{day:<8} {int(stats['总交易日数']):<10} {int(stats['上涨天数']):<10} "
                  f"{stats['上涨概率']:<12.2%} {stats['平均收益率']:<12.4%} {stats['收益率标准差']:<12.4%}")
    
    print("\n主要发现:")
    print("-" * 40)
    
    # 找出上涨概率最高和最低的日子
    valid_stats = weekly_stats.dropna()
    if not valid_stats.empty:
        max_prob_day = valid_stats['上涨概率'].idxmax()
        min_prob_day = valid_stats['上涨概率'].idxmin()
        max_return_day = valid_stats['平均收益率'].idxmax()
        min_return_day = valid_stats['平均收益率'].idxmin()
        
        print(f"1. 上涨概率最高: {max_prob_day} ({valid_stats.loc[max_prob_day, '上涨概率']:.2%})")
        print(f"2. 上涨概率最低: {min_prob_day} ({valid_stats.loc[min_prob_day, '上涨概率']:.2%})")
        print(f"3. 平均收益率最高: {max_return_day} ({valid_stats.loc[max_return_day, '平均收益率']:.4%})")
        print(f"4. 平均收益率最低: {min_return_day} ({valid_stats.loc[min_return_day, '平均收益率']:.4%})")
        
        # 计算概率差异
        prob_range = valid_stats['上涨概率'].max() - valid_stats['上涨概率'].min()
        print(f"5. 各工作日上涨概率差异: {prob_range:.2%}")
        
        if prob_range > 0.1:  # 如果差异超过10%
            print("   -> 不同工作日的上涨概率存在较明显差异")
        else:
            print("   -> 不同工作日的上涨概率相对均衡")

def main():
    """
    主函数
    """
    print("最终版：中证1000指数每日上涨概率统计程序")
    print("=" * 60)
    
    # 1. 获取数据
    df = get_csi1000_data()
    if df is None:
        print("程序终止：无法获取数据")
        return
    
    # 2. 计算收益率和涨跌情况
    df = calculate_daily_returns(df)
    
    # 3. 分析每个工作日的上涨概率
    weekly_stats = analyze_weekly_probability(df)
    
    # 4. 打印详细分析
    print_detailed_analysis(df, weekly_stats)
    
    # 5. 创建可视化图表
    create_visualizations(df, weekly_stats)
    
    # 6. 保存结果到CSV文件
    weekly_stats.to_csv('最终版_中证1000指数每日上涨概率统计.csv', encoding='utf-8-sig')
    df.to_csv('最终版_中证1000指数历史数据.csv', encoding='utf-8-sig', index=False)
    
    print("\n💾 结果已保存到:")
    print("  - 最终版_中证1000指数每日上涨概率统计.csv")
    print("  - 最终版_中证1000指数历史数据.csv")
    print("  - 最终版_中证1000指数每日上涨概率分析.png")
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main()
