Metadata-Version: 2.4
Name: stock-statistics
Version: 0.1.0
Summary: 中证1000指数每日上涨概率统计
Author-email: User <<EMAIL>>
License: MIT
Requires-Python: >=3.11
Requires-Dist: akshare>=1.17.34
Requires-Dist: matplotlib>=3.10.5
Requires-Dist: numpy>=2.3.2
Requires-Dist: pandas>=2.0.0
Requires-Dist: plotly>=5.17.0
Requires-Dist: seaborn>=0.12.0
Requires-Dist: streamlit>=1.28.0
Description-Content-Type: text/markdown

# 中证1000指数每日上涨概率统计

这个项目用于统计中证1000指数在一个星期的每一天（工作日）上涨的概率，使用akshare获取最近一年的数据。

## 功能特点

- 📊 获取中证1000指数最近一年的历史数据
- 📈 计算每个工作日的上涨概率
- 📉 分析平均收益率和波动性
- 🎨 生成可视化图表
- 📋 导出详细统计报告

## 安装依赖

本项目使用 `uv` 作为包管理器。请确保已安装 `uv`：

```bash
# 安装 uv (如果尚未安装)
pip install uv

# 安装项目依赖
uv sync
```

## 使用方法

### 🚀 推荐方法：运行最终修复版（解决了网络问题）

```bash
# 运行最终修复版程序（已解决akshare连接问题）
uv run python final_analysis.py
```

### 🔧 网络问题修复版

```bash
# 专门解决akshare连接问题的版本
uv run python akshare_fix.py
```

### 📊 其他版本

```bash
# 运行原始完整版程序
uv run python main.py

# 运行网络优化版程序
uv run python network_optimized.py

# 运行简化版程序
uv run python simple_analysis.py

# 测试程序功能
uv run python test_analysis.py
```

## 输出结果

程序运行后会生成以下文件：

1. **中证1000指数每日上涨概率分析.png** - 包含4个子图的可视化分析：
   - 各工作日上涨概率柱状图
   - 各工作日平均收益率柱状图
   - 各工作日交易日数统计
   - 各工作日收益率分布箱线图

2. **中证1000指数每日上涨概率统计.csv** - 详细的统计数据表格

3. **中证1000指数历史数据.csv** - 原始历史数据

## 分析指标

- **上涨概率**: 每个工作日上涨的概率（收益率 > 0）
- **平均收益率**: 每个工作日的平均日收益率
- **收益率标准差**: 收益率的波动性指标
- **交易日数**: 统计期间内每个工作日的交易天数

## 数据源

- 使用 [akshare](https://akshare.akfamily.xyz/) 获取中证1000指数数据
- 指数代码: 000852
- 数据时间范围: 最近一年

## 依赖包

- `akshare`: 金融数据接口
- `pandas`: 数据处理
- `numpy`: 数值计算
- `matplotlib`: 图表绘制
- `seaborn`: 统计图表

## 程序说明

### 主要文件

- **final_analysis.py**: 🌟 **最终修复版**，解决了akshare连接问题，推荐使用
- **akshare_fix.py**: 🔧 **网络问题修复版**，专门解决akshare连接失败问题
- **main.py**: 完整版程序，包含数据获取、分析、可视化等全部功能
- **network_optimized.py**: 网络优化版程序，包含多种数据源尝试
- **simple_analysis.py**: 简化版程序，专门用于获取真实数据
- **test_analysis.py**: 测试程序，用于验证输出文件和数据质量

### 🚀 核心功能特点

- **✅ 解决网络问题**: 成功解决akshare连接失败问题，使用最稳定的东方财富数据源
- **📊 完整统计分析**: 计算每个工作日的上涨概率、平均收益率、波动性等指标
- **📈 可视化图表**: 生成包含4个子图的综合分析图表（概率、收益率、交易日数、分布）
- **💾 数据导出**: 自动保存CSV格式的统计结果和原始数据
- **🔄 多重备选方案**: 提供多个版本程序，确保在不同网络环境下都能正常运行
- **📅 真实数据**: 基于2023-2024年真实交易数据进行分析

### 输出文件说明

1. **中证1000指数每日上涨概率分析.png**: 可视化分析图表
2. **中证1000指数每日上涨概率统计.csv**: 按工作日统计的概率数据
3. **中证1000指数历史数据.csv**: 完整的历史价格数据

## 注意事项

- 程序只分析工作日数据（周一至周五）
- 需要网络连接以获取最新数据
- 首次运行可能需要较长时间下载数据
- 如果网络获取失败，程序会自动使用模拟数据进行演示
- 图表使用非交互式后端，适合在服务器环境运行

## 🎯 实际分析结果

基于2023年至2024年真实数据的分析结果显示：

- **星期一**上涨概率最高：**59.38%**
- **星期三**上涨概率最低：**40.21%**
- **星期四**平均收益率最高：**0.35%**
- **星期三**平均收益率最低：**-0.17%**
- 不同工作日的上涨概率差异：**19.17%**

### 📈 关键发现

1. **周一效应明显**：星期一的上涨概率显著高于其他工作日
2. **周三表现最弱**：无论是上涨概率还是平均收益率都是最低的
3. **周四收益最佳**：虽然上涨概率中等，但平均收益率最高
4. **整体上涨概率**：49.07%，略低于50%
5. **日均收益率**：0.0568%，年化约14%

## 许可证

MIT License
